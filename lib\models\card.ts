import { Document, ObjectId } from "mongodb";

export interface Card extends Document {
  _id?: ObjectId;
  userId: ObjectId;
  accountId: ObjectId;
  cardNumber: string; // Encrypted/masked
  cardType: "virtual" | "physical";
  cardTier: "standard" | "gold" | "platinum" | "black";
  cardName: string;
  expiryDate: Date;
  cvv: string; // Encrypted
  status: "active" | "inactive" | "blocked" | "expired";
  dailyLimit: number;
  monthlyLimit: number;
  currency: string;
  issuanceFee: number;
  cashbackRate: number;
  features: string[];
  billingAddress?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface CardDetails {
  id: string;
  userId: string;
  accountId: string;
  cardNumber: string; // Masked version
  cardType: "virtual" | "physical";
  cardTier: "standard" | "gold" | "platinum" | "black";
  cardName: string;
  expiryDate: Date;
  status: "active" | "inactive" | "blocked" | "expired";
  dailyLimit: number;
  monthlyLimit: number;
  currency: string;
  issuanceFee: number;
  cashbackRate: number;
  features: string[];
  billingAddress?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
}

export interface CreateCardData {
  userId: string;
  accountId: string;
  cardType: "virtual" | "physical";
  cardTier: "standard" | "gold" | "platinum" | "black";
  dailyLimit?: number;
  currency?: string;
  billingAddress?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
}

export interface CardApplication {
  cardType: "virtual" | "physical";
  cardTier: "standard" | "gold" | "platinum" | "black";
  currency: string;
  dailyLimit: string;
  billingAddress?: string;
  billingCity?: string;
  billingState?: string;
  billingZip?: string;
  annualIncome?: string;
  employment?: string;
  housingStatus?: string;
}

export const CARD_TIERS = {
  standard: {
    name: "Standard Virtual Card",
    issuanceFee: 5.00,
    cashbackRate: 1,
    features: [
      "Instant issuance",
      "1% cashback on all purchases",
      "Basic fraud protection",
      "Mobile app management",
    ],
  },
  gold: {
    name: "Gold Virtual Card",
    issuanceFee: 15.00,
    cashbackRate: 2,
    features: [
      "Instant issuance",
      "2% cashback on all purchases",
      "Enhanced security",
      "Priority support",
    ],
  },
  platinum: {
    name: "Platinum Virtual Card",
    issuanceFee: 25.00,
    cashbackRate: 3,
    features: [
      "Instant issuance",
      "3% cashback on all purchases",
      "Premium security features",
      "Concierge service",
    ],
  },
  black: {
    name: "Black Virtual Card",
    issuanceFee: 50.00,
    cashbackRate: 5,
    features: [
      "Instant issuance",
      "5% cashback on all purchases",
      "Maximum security",
      "VIP support",
    ],
  },
} as const;
