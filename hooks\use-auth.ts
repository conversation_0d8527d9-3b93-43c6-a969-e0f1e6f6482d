"use client";

import {
   getUserProfile,
   loginUser,
   registerUser,
   verifyPin,
} from "@/lib/actions";
import { CreateUserData, UserProfile } from "@/lib/models";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";

// Local storage keys
const AUTH_STORAGE_KEY = "paramount-auth";
const PIN_STORAGE_KEY = "paramount-pin-verified";

interface AuthState {
   isAuthenticated: boolean;
   isPinVerified: boolean;
   isHydrated: boolean;
   isLoading: boolean;
   isCheckingAuth: boolean;
   user: UserProfile | null;
}

/**
 * Get auth state from localStorage
 */
function getStoredAuthState(): AuthState {
   if (typeof window === "undefined") {
      return {
         isAuthenticated: false,
         isPinVerified: false,
         isHydrated: false,
         isLoading: false,
         isCheckingAuth: false,
         user: null,
      };
   }

   try {
      const stored = localStorage.getItem(AUTH_STORAGE_KEY);
      const pinVerified = localStorage.getItem(PIN_STORAGE_KEY) === "true";

      if (stored) {
         const authData = JSON.parse(stored);
         return {
            isAuthenticated: !!authData.user,
            isPinVerified: pinVerified,
            isHydrated: true,
            isLoading: false,
            isCheckingAuth: false,
            user: authData.user || null,
         };
      }
   } catch (error) {
      console.error("Error reading auth state from localStorage:", error);
   }

   return {
      isAuthenticated: false,
      isPinVerified: false,
      isHydrated: true,
      isLoading: false,
      isCheckingAuth: false,
      user: null,
   };
}

/**
 * Save auth state to localStorage
 */
function saveAuthState(state: AuthState) {
   if (typeof window === "undefined") return;

   try {
      localStorage.setItem(
         AUTH_STORAGE_KEY,
         JSON.stringify({ user: state.user })
      );
      localStorage.setItem(PIN_STORAGE_KEY, state.isPinVerified.toString());
   } catch (error) {
      console.error("Error saving auth state to localStorage:", error);
   }
}

/**
 * Clear auth state from localStorage
 */
function clearAuthState() {
   if (typeof window === "undefined") return;

   try {
      localStorage.removeItem(AUTH_STORAGE_KEY);
      localStorage.removeItem(PIN_STORAGE_KEY);
   } catch (error) {
      console.error("Error clearing auth state from localStorage:", error);
   }
}

/**
 * Main authentication hook
 */
export function useAuth() {
   const queryClient = useQueryClient();
   const [authState, setAuthState] = useState<AuthState>({
      isAuthenticated: false,
      isPinVerified: false,
      isHydrated: false,
      isLoading: false,
      isCheckingAuth: false,
      user: null,
   });
   // Hydrate from localStorage on mount
   useEffect(() => {
      const storedState = getStoredAuthState();
      setAuthState(() => ({
         ...storedState,
         isHydrated: true,
      }));
   }, []);

   // Sync with localStorage on state changes (only after hydration)
   useEffect(() => {
      if (authState.isHydrated) {
         saveAuthState(authState);
      }
   }, [authState]);

   // Login mutation
   const loginMutation = useMutation({
      mutationFn: async (credentials: { email: string; password: string }) => {
         const result = await loginUser(
            credentials.email,
            credentials.password
         );
         if (!result.success) {
            throw new Error(result.message || "Login failed");
         }
         return result.user!;
      },
      onSuccess: (user) => {
         setAuthState((prev) => ({
            ...prev,
            isAuthenticated: true,
            isPinVerified: false, // Reset PIN verification on new login
            isLoading: false,
            user,
         }));
         // Invalidate user-related queries
         queryClient.invalidateQueries({ queryKey: ["user"] });
      },
      onMutate: () => {
         setAuthState((prev) => ({ ...prev, isLoading: true }));
      },
      onError: () => {
         setAuthState((prev) => ({ ...prev, isLoading: false }));
      },
   });

   // Register mutation
   const registerMutation = useMutation({
      mutationFn: async (userData: CreateUserData) => {
         const result = await registerUser(userData);
         if (!result.success) {
            throw new Error(result.message || "Registration failed");
         }
         return result.user!;
      },
      onSuccess: (user) => {
         setAuthState((prev) => ({
            ...prev,
            isAuthenticated: true,
            isPinVerified: false, // PIN needs to be verified after registration
            user,
         }));
         // Invalidate user-related queries
         queryClient.invalidateQueries({ queryKey: ["user"] });
      },
   });

   // PIN verification mutation
   const verifyPinMutation = useMutation({
      mutationFn: async (pin: string) => {
         if (!authState.user) {
            throw new Error("User not authenticated");
         }
         const result = await verifyPin(authState.user.id, pin);
         if (!result.success) {
            throw new Error(result.message || "PIN verification failed");
         }
         return true;
      },
      onSuccess: () => {
         setAuthState((prev) => ({
            ...prev,
            isPinVerified: true,
         }));
      },
   });

   // Logout function
   const logout = () => {
      setAuthState((prev) => ({
         ...prev,
         isAuthenticated: false,
         isPinVerified: false,
         isLoading: false,
         isCheckingAuth: false,
         user: null,
      }));
      clearAuthState();
      // Clear all cached data
      queryClient.clear();
   };

   // Check auth status function
   const checkAuthStatus = async () => {
      setAuthState((prev) => ({ ...prev, isCheckingAuth: true }));

      try {
         // Simulate auth check delay
         await new Promise((resolve) => setTimeout(resolve, 500));

         // In real app, this would validate the stored token/session
         if (authState.isAuthenticated && authState.user) {
            // Validate session is still valid
            // For now, just keep the current state
            setAuthState((prev) => ({ ...prev, isCheckingAuth: false }));
         } else {
            setAuthState((prev) => ({
               ...prev,
               isAuthenticated: false,
               isPinVerified: false,
               user: null,
               isCheckingAuth: false,
            }));
         }
      } catch (error) {
         console.error("Error checking auth status:", error);
         setAuthState((prev) => ({
            ...prev,
            isAuthenticated: false,
            isPinVerified: false,
            user: null,
            isCheckingAuth: false,
         }));
      }
   };

   // Get user profile query (only if authenticated)
   const userProfileQuery = useQuery({
      queryKey: ["user", "profile", authState.user?.id],
      queryFn: async () => {
         if (!authState.user) throw new Error("User not authenticated");
         const result = await getUserProfile(authState.user.id);
         if (!result.success) {
            throw new Error(result.message || "Failed to get user profile");
         }
         return result.user!;
      },
      enabled: !!authState.user?.id,
      staleTime: 5 * 60 * 1000, // 5 minutes
   });

   return {
      // State
      isAuthenticated: authState.isAuthenticated,
      isPinVerified: authState.isPinVerified,
      isHydrated: authState.isHydrated,
      isLoading: authState.isLoading,
      isCheckingAuth: authState.isCheckingAuth,
      user: authState.user,

      // Mutations
      login: loginMutation.mutateAsync,
      register: registerMutation.mutateAsync,
      verifyPin: verifyPinMutation.mutateAsync,
      logout,
      checkAuthStatus,

      // Loading states
      isLoggingIn: loginMutation.isPending,
      isRegistering: registerMutation.isPending,
      isVerifyingPin: verifyPinMutation.isPending,

      // Errors
      loginError: loginMutation.error?.message,
      registerError: registerMutation.error?.message,
      pinError: verifyPinMutation.error?.message,

      // User profile
      userProfile: userProfileQuery.data,
      isLoadingProfile: userProfileQuery.isLoading,
      profileError: userProfileQuery.error?.message,

      // Reset functions
      resetLoginError: loginMutation.reset,
      resetRegisterError: registerMutation.reset,
      resetPinError: verifyPinMutation.reset,
   };
}
