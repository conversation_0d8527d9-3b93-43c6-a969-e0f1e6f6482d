"use client";

import { Input } from "@/components/ui/input";
import { motion } from "framer-motion";
import { <PERSON>, Eye, EyeOff, Lock, X } from "lucide-react";
import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";

// Step 4 Schema
export const securitySchema = z
   .object({
      password: z
         .string()
         .min(8, "Password must be at least 8 characters")
         .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
         .regex(/[0-9]/, "Password must contain at least one number")
         .regex(
            /[^A-Za-z0-9]/,
            "Password must contain at least one special character"
         ),
      confirmPassword: z.string(),
      agreedToTerms: z
         .boolean()
         .refine(
            (val) => val === true,
            "You must agree to the terms and conditions"
         ),
   })
   .refine((data) => data.password === data.confirmPassword, {
      message: "Passwords don't match",
      path: ["confirmPassword"],
   });

export type SecurityForm = z.infer<typeof securitySchema>;

interface SecurityStepProps {
   form: UseFormReturn<SecurityForm>;
}

const getPasswordStrength = (password: string) => {
   const checks = [
      password.length >= 8,
      /[A-Z]/.test(password),
      /[0-9]/.test(password),
      /[^A-Za-z0-9]/.test(password),
   ];
   return checks.filter(Boolean).length;
};

export function SecurityStep({ form }: SecurityStepProps) {
   const [showPassword, setShowPassword] = useState(false);
   const [showConfirmPassword, setShowConfirmPassword] = useState(false);

   const password = form.watch("password") || "";
   const passwordStrength = getPasswordStrength(password);

   return (
      <motion.div
         key="step4"
         initial={{ opacity: 0, x: 20 }}
         animate={{ opacity: 1, x: 0 }}
         exit={{ opacity: 0, x: -20 }}
         className="space-y-6"
      >
         <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-foreground mb-2">
               Security
            </h2>
            <p className="text-muted-foreground">
               Protect your account with a strong password
            </p>
         </div>

         <div className="space-y-4">
            <div className="relative">
               <Input
                  label="Password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Create a strong password"
                  icon={<Lock size={18} />}
                  error={form.formState.errors.password?.message}
                  {...form.register("password")}
               />
               <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-9 text-muted-foreground hover:text-foreground transition-colors"
               >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
               </button>
            </div>

            {/* Password strength indicator */}
            {password && (
               <div className="space-y-2">
                  <div className="flex items-center gap-2">
                     <div className="text-sm text-muted-foreground">
                        Password strength:
                     </div>
                     <div
                        className={`text-sm font-medium ${
                           passwordStrength < 2
                              ? "text-destructive"
                              : passwordStrength < 4
                              ? "text-warning"
                              : "text-success"
                        }`}
                     >
                        {passwordStrength < 2
                           ? "Weak"
                           : passwordStrength < 4
                           ? "Medium"
                           : "Strong"}
                     </div>
                  </div>

                  <div className="space-y-1 text-xs">
                     <div
                        className={`flex items-center gap-2 ${
                           password.length >= 8
                              ? "text-success"
                              : "text-muted-foreground"
                        }`}
                     >
                        {password.length >= 8 ? (
                           <Check size={12} />
                        ) : (
                           <X size={12} />
                        )}
                        At least 8 characters
                     </div>
                     <div
                        className={`flex items-center gap-2 ${
                           /[A-Z]/.test(password)
                              ? "text-success"
                              : "text-muted-foreground"
                        }`}
                     >
                        {/[A-Z]/.test(password) ? (
                           <Check size={12} />
                        ) : (
                           <X size={12} />
                        )}
                        One uppercase letter
                     </div>
                     <div
                        className={`flex items-center gap-2 ${
                           /[0-9]/.test(password)
                              ? "text-success"
                              : "text-muted-foreground"
                        }`}
                     >
                        {/[0-9]/.test(password) ? (
                           <Check size={12} />
                        ) : (
                           <X size={12} />
                        )}
                        One number
                     </div>
                     <div
                        className={`flex items-center gap-2 ${
                           /[^A-Za-z0-9]/.test(password)
                              ? "text-success"
                              : "text-muted-foreground"
                        }`}
                     >
                        {/[^A-Za-z0-9]/.test(password) ? (
                           <Check size={12} />
                        ) : (
                           <X size={12} />
                        )}
                        One special character
                     </div>
                  </div>
               </div>
            )}
         </div>

         <div className="relative">
            <Input
               label="Confirm Password"
               type={showConfirmPassword ? "text" : "password"}
               placeholder="Confirm your password"
               icon={<Lock size={18} />}
               error={form.formState.errors.confirmPassword?.message}
               {...form.register("confirmPassword")}
            />
            <button
               type="button"
               onClick={() => setShowConfirmPassword(!showConfirmPassword)}
               className="absolute right-3 top-9 text-muted-foreground hover:text-foreground transition-colors"
            >
               {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
            </button>
         </div>

         <div className="flex items-start gap-3">
            <input
               type="checkbox"
               id="terms"
               {...form.register("agreedToTerms")}
               className="mt-1"
            />
            <label htmlFor="terms" className="text-sm text-muted-foreground">
               I agree to the{" "}
               <button
                  type="button"
                  className="text-primary hover:text-primary-light underline"
               >
                  Terms of Service
               </button>{" "}
               and{" "}
               <button
                  type="button"
                  className="text-primary hover:text-primary-light underline"
               >
                  Privacy Policy
               </button>
            </label>
         </div>
         {form.formState.errors.agreedToTerms && (
            <p className="text-sm text-destructive">
               {form.formState.errors.agreedToTerms.message}
            </p>
         )}
      </motion.div>
   );
}
