import { UserProfile } from "@/lib/models";

/**
 * Role-based access control utilities
 */

export type Role = "user" | "admin";

export interface RolePermissions {
  canManageUsers: boolean;
  canManageTransactions: boolean;
  canManageAccounts: boolean;
  canViewAllTransactions: boolean;
  canModifyBalances: boolean;
  canManageNotifications: boolean;
  canChangeUserRoles: boolean;
}

/**
 * Get permissions for a role
 */
export function getRolePermissions(role: Role): RolePermissions {
  switch (role) {
    case "admin":
      return {
        canManageUsers: true,
        canManageTransactions: true,
        canManageAccounts: true,
        canViewAllTransactions: true,
        canModifyBalances: true,
        canManageNotifications: true,
        canChangeUserRoles: true,
      };
    case "user":
    default:
      return {
        canManageUsers: false,
        canManageTransactions: false,
        canManageAccounts: false,
        canViewAllTransactions: false,
        canModifyBalances: false,
        canManageNotifications: false,
        canChangeUserRoles: false,
      };
  }
}

/**
 * Check if user has a specific permission
 */
export function hasPermission(
  user: UserProfile | null,
  permission: keyof RolePermissions
): boolean {
  if (!user) return false;
  
  const permissions = getRolePermissions(user.role);
  return permissions[permission];
}

/**
 * Check if user is admin
 */
export function isAdmin(user: UserProfile | null): boolean {
  return user?.role === "admin";
}

/**
 * Check if user can access admin routes
 */
export function canAccessAdminRoutes(user: UserProfile | null): boolean {
  return isAdmin(user);
}

/**
 * Require admin role - throws error if not admin
 */
export function requireAdmin(user: UserProfile | null): void {
  if (!isAdmin(user)) {
    throw new Error("Admin access required");
  }
}

/**
 * Require specific permission - throws error if not permitted
 */
export function requirePermission(
  user: UserProfile | null,
  permission: keyof RolePermissions
): void {
  if (!hasPermission(user, permission)) {
    throw new Error(`Permission required: ${permission}`);
  }
}
