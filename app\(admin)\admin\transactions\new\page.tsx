"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/hooks";
import { useUsers } from "@/hooks/use-admin";
import { createNewTransaction, getUserAccount } from "@/lib/actions";
import { motion } from "framer-motion";
import { ArrowLeft, DollarSign, Plus, User } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

export default function NewTransaction() {
   const { user } = useAuth();
   const router = useRouter();
   const [isSubmitting, setIsSubmitting] = useState(false);

   // Get users for selection
   const { data: usersData } = useUsers({
      page: 1,
      limit: 100,
   });

   const users = usersData?.users || [];

   const [formData, setFormData] = useState({
      userId: "",
      type: "credit" as "credit" | "debit",
      amount: "",
      description: "",
      category: "",
      merchant: "",
      reference: "",
      status: "completed" as "pending" | "completed",
   });

   // Date/time state
   const [useCurrentDateTime, setUseCurrentDateTime] = useState(true);
   const [customDate, setCustomDate] = useState("");
   const [customTime, setCustomTime] = useState("");

   // Initialize custom date/time with current values
   useState(() => {
      const now = new Date();
      const dateStr = now.toISOString().split("T")[0]; // YYYY-MM-DD
      const timeStr = now.toTimeString().slice(0, 5); // HH:MM
      setCustomDate(dateStr);
      setCustomTime(timeStr);
   });

   const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();

      if (!user?.id) {
         toast.error("Admin user not found");
         return;
      }

      if (!formData.userId || !formData.amount || !formData.description) {
         toast.error("Please fill in all required fields");
         return;
      }

      setIsSubmitting(true);

      try {
         const selectedUser = users.find((u) => u.id === formData.userId);
         if (!selectedUser) {
            toast.error("Selected user not found");
            return;
         }

         // Get the user's account first
         console.log("userId: ", formData.userId);
         const userAccountResult = await getUserAccount(formData.userId);

         if (!userAccountResult.success || !userAccountResult.account) {
            toast.error(
               "Failed to get user account: " + userAccountResult.message
            );
            return;
         }

         // Prepare custom date if not using current date/time
         let transactionDate: Date | undefined;
         if (!useCurrentDateTime && customDate && customTime) {
            transactionDate = new Date(`${customDate}T${customTime}:00`);
         }

         const result = await createNewTransaction({
            userId: formData.userId,
            accountId: userAccountResult.account.id,
            type: formData.type,
            amount: parseFloat(formData.amount),
            currency: "USD",
            description: formData.description,
            category: formData.category || "Admin Transaction",
            merchant: formData.merchant,
            reference: formData.reference,
            status: formData.status,
            customDate: transactionDate,
            metadata: {
               createdByAdmin: user.id,
               adminName: `${user.firstName} ${user.lastName}`,
            },
         });

         if (result.success) {
            toast.success("Transaction created successfully");
            router.push("/admin/transactions");
         } else {
            toast.error(result.message || "Failed to create transaction");
         }
      } catch (error) {
         console.error("Transaction creation error:", error);
         toast.error("Failed to create transaction");
      } finally {
         setIsSubmitting(false);
      }
   };

   const handleInputChange = (field: string, value: string) => {
      setFormData((prev) => ({
         ...prev,
         [field]: value,
      }));
   };

   return (
      <div className="container mx-auto px-4 py-8">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
         >
            {/* Header */}
            <div className="mb-8">
               <div className="flex items-center gap-4 mb-4">
                  <Button
                     variant="outline"
                     size="sm"
                     onClick={() => router.back()}
                  >
                     <ArrowLeft className="h-4 w-4 mr-2" />
                     Back
                  </Button>
               </div>
               <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                     <Plus className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                     <h1 className="text-3xl font-bold text-foreground">
                        Create New Transaction
                     </h1>
                     <p className="text-muted-foreground">
                        Create a new transaction for a user account.
                     </p>
                  </div>
               </div>
            </div>

            {/* Form */}
            <Card>
               <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                     <DollarSign className="h-5 w-5" />
                     Transaction Details
                  </CardTitle>
               </CardHeader>
               <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                     {/* User Selection */}
                     <div className="space-y-2">
                        <Label htmlFor="userId">
                           Select User{" "}
                           <span className="text-destructive">*</span>
                        </Label>
                        <Select
                           value={formData.userId}
                           onValueChange={(value) =>
                              handleInputChange("userId", value)
                           }
                        >
                           <SelectTrigger>
                              <SelectValue placeholder="Choose a user" />
                           </SelectTrigger>
                           <SelectContent>
                              {users.map((user) => (
                                 <SelectItem key={user.id} value={user.id}>
                                    <div className="flex items-center gap-2">
                                       <User className="h-4 w-4" />
                                       {user.firstName} {user.lastName} (
                                       {user.email})
                                    </div>
                                 </SelectItem>
                              ))}
                           </SelectContent>
                        </Select>
                     </div>

                     {/* Transaction Type */}
                     <div className="space-y-2">
                        <Label htmlFor="type">
                           Transaction Type{" "}
                           <span className="text-destructive">*</span>
                        </Label>
                        <Select
                           value={formData.type}
                           onValueChange={(value: "credit" | "debit") =>
                              handleInputChange("type", value)
                           }
                        >
                           <SelectTrigger>
                              <SelectValue />
                           </SelectTrigger>
                           <SelectContent>
                              <SelectItem value="credit">
                                 Credit (Money In)
                              </SelectItem>
                              <SelectItem value="debit">
                                 Debit (Money Out)
                              </SelectItem>
                           </SelectContent>
                        </Select>
                     </div>

                     {/* Amount */}
                     <div className="space-y-2">
                        <Label htmlFor="amount">
                           Amount <span className="text-destructive">*</span>
                        </Label>
                        <Input
                           id="amount"
                           type="number"
                           step="0.01"
                           min="0"
                           placeholder="0.00"
                           value={formData.amount}
                           onChange={(e) =>
                              handleInputChange("amount", e.target.value)
                           }
                        />
                     </div>

                     {/* Description */}
                     <div className="space-y-2">
                        <Label htmlFor="description">
                           Description{" "}
                           <span className="text-destructive">*</span>
                        </Label>
                        <Textarea
                           id="description"
                           placeholder="Enter transaction description"
                           value={formData.description}
                           onChange={(e) =>
                              handleInputChange("description", e.target.value)
                           }
                        />
                     </div>

                     {/* Category */}
                     <div className="space-y-2">
                        <Label htmlFor="category">Category</Label>
                        <Input
                           id="category"
                           placeholder="e.g., Admin Adjustment, Refund, Bonus"
                           value={formData.category}
                           onChange={(e) =>
                              handleInputChange("category", e.target.value)
                           }
                        />
                     </div>

                     {/* Merchant */}
                     <div className="space-y-2">
                        <Label htmlFor="merchant">Merchant/Source</Label>
                        <Input
                           id="merchant"
                           placeholder="e.g., Paramount Bank, Admin"
                           value={formData.merchant}
                           onChange={(e) =>
                              handleInputChange("merchant", e.target.value)
                           }
                        />
                     </div>

                     {/* Reference */}
                     <div className="space-y-2">
                        <Label htmlFor="reference">Reference Number</Label>
                        <Input
                           id="reference"
                           placeholder="Optional reference number"
                           value={formData.reference}
                           onChange={(e) =>
                              handleInputChange("reference", e.target.value)
                           }
                        />
                     </div>

                     {/* Status */}
                     <div className="space-y-2">
                        <Label htmlFor="status">Status</Label>
                        <Select
                           value={formData.status}
                           onValueChange={(value: "pending" | "completed") =>
                              handleInputChange("status", value)
                           }
                        >
                           <SelectTrigger>
                              <SelectValue />
                           </SelectTrigger>
                           <SelectContent>
                              <SelectItem value="completed">
                                 Completed
                              </SelectItem>
                              <SelectItem value="pending">Pending</SelectItem>
                           </SelectContent>
                        </Select>
                     </div>

                     {/* Date and Time Settings */}
                     <div className="space-y-4 p-4 border rounded-lg bg-muted/30">
                        <div className="flex items-center justify-between">
                           <div>
                              <Label className="text-base font-medium">
                                 Transaction Date & Time
                              </Label>
                              <p className="text-sm text-muted-foreground">
                                 Choose when this transaction should be recorded
                              </p>
                           </div>
                           <div className="flex items-center space-x-2">
                              <Label
                                 htmlFor="use-current-time"
                                 className="text-sm"
                              >
                                 Use current date/time
                              </Label>
                              <Switch
                                 id="use-current-time"
                                 checked={useCurrentDateTime}
                                 onCheckedChange={setUseCurrentDateTime}
                              />
                           </div>
                        </div>

                        {!useCurrentDateTime && (
                           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                 <Label htmlFor="custom-date">Date</Label>
                                 <Input
                                    id="custom-date"
                                    type="date"
                                    value={customDate}
                                    onChange={(e) =>
                                       setCustomDate(e.target.value)
                                    }
                                 />
                              </div>
                              <div className="space-y-2">
                                 <Label htmlFor="custom-time">Time</Label>
                                 <Input
                                    id="custom-time"
                                    type="time"
                                    value={customTime}
                                    onChange={(e) =>
                                       setCustomTime(e.target.value)
                                    }
                                 />
                              </div>
                           </div>
                        )}

                        {useCurrentDateTime && (
                           <div className="text-sm text-muted-foreground">
                              Transaction will be recorded with current date and
                              time: {new Date().toLocaleString()}
                           </div>
                        )}
                     </div>

                     {/* Submit Buttons */}
                     <div className="flex items-center gap-4 pt-6">
                        <Button
                           type="submit"
                           disabled={isSubmitting}
                           className="flex-1"
                        >
                           {isSubmitting ? "Creating..." : "Create Transaction"}
                        </Button>
                        <Button
                           type="button"
                           variant="outline"
                           onClick={() => router.back()}
                           disabled={isSubmitting}
                        >
                           Cancel
                        </Button>
                     </div>
                  </form>
               </CardContent>
            </Card>
         </motion.div>
      </div>
   );
}
