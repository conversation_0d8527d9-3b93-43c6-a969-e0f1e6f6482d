import { create } from "zustand";
import { persist } from "zustand/middleware";

interface User {
   id: string;
   email: string;
   firstName: string;
   lastName: string;
   username: string;
   phoneNumber: string;
   country: string;
   accountType: string;
   role: "user" | "admin";
   verificationStatus: "verified" | "pending" | "unverified";
}

interface AuthState {
   isAuthenticated: boolean;
   isPinVerified: boolean;
   isHydrated: boolean;
   isLoading: boolean;
   isCheckingAuth: boolean;
   user: User | null;
   login: (email: string, password: string) => Promise<boolean>;
   verifyPin: (pin: string) => Promise<boolean>;
   logout: () => void;
   register: (
      userData: Partial<User> & { email: string; password: string }
   ) => Promise<boolean>;
   checkAuthStatus: () => Promise<void>;
   setHydrated: (hydrated: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
   persist(
      (set, get) => ({
         isAuthenticated: false,
         isPinVerified: false,
         isHydrated: false,
         isLoading: false,
         isCheckingAuth: false,
         user: null,

         login: async (email: string, password: string) => {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Simple demo login - in real app, this would call your API
            if (email && password) {
               const user: User = {
                  id: "1",
                  email,
                  firstName: "John",
                  lastName: "Doe",
                  username: "johndoe",
                  phoneNumber: "+**********",
                  country: "United States",
                  accountType: "Checking Account",
                  role: "user",
                  verificationStatus: "verified",
               };

               set({ isAuthenticated: true, user });
               return true;
            }
            return false;
         },

         verifyPin: async (pin: string) => {
            const state = get();
            if (!state.user) {
               throw new Error("User not authenticated");
            }

            // Use actual PIN verification API
            const { verifyPin: verifyUserPin } = await import("@/lib/actions");
            const result = await verifyUserPin(state.user.id, pin);

            if (result.success) {
               set({ isPinVerified: true });
               return true;
            } else {
               throw new Error(result.message || "PIN verification failed");
            }
         },

         logout: () => {
            set({ isAuthenticated: false, isPinVerified: false, user: null });
         },

         register: async (userData) => {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 1500));

            const user: User = {
               id: Date.now().toString(),
               firstName: userData.firstName || "",
               lastName: userData.lastName || "",
               username: userData.username || "",
               email: userData.email,
               phoneNumber: userData.phoneNumber || "",
               country: userData.country || "",
               accountType: userData.accountType || "Checking Account",
               role: "user",
               verificationStatus: "verified",
            };

            set({ isAuthenticated: true, user });
            return true;
         },

         checkAuthStatus: async () => {
            set({ isCheckingAuth: true });

            try {
               // Simulate auth check delay
               await new Promise((resolve) => setTimeout(resolve, 1000));

               // In real app, this would validate the stored token/session
               const { user, isAuthenticated } = get();

               if (isAuthenticated && user) {
                  // Validate session is still valid
                  // For now, just keep the current state
                  set({ isCheckingAuth: false });
               } else {
                  set({
                     isAuthenticated: false,
                     isPinVerified: false,
                     user: null,
                     isCheckingAuth: false,
                  });
               }
            } catch (error) {
               set({
                  isAuthenticated: false,
                  isPinVerified: false,
                  user: null,
                  isCheckingAuth: false,
               });
            }
         },

         setHydrated: (hydrated: boolean) => {
            set({ isHydrated: hydrated });
         },
      }),
      {
         name: "paramount-auth",
         partialize: (state) => ({
            isAuthenticated: state.isAuthenticated,
            isPinVerified: state.isPinVerified,
            user: state.user,
         }),
      }
   )
);
