import { Document, ObjectId } from "mongodb";

export interface User<PERSON><PERSON> extends Document {
   _id?: ObjectId;
   userId: ObjectId;
   isBlocked: boolean;
   blockTitle: string;
   blockMessage: string;
   blockReason?: string;
   blockedBy: ObjectId; // Admin who blocked the user
   blockedAt: Date;
   unblockedBy?: ObjectId; // Admin who unblocked the user
   unblockedAt?: Date;
   createdAt: Date;
   updatedAt: Date;
}

export interface UserBlockDetails {
   id: string;
   userId: string;
   isBlocked: boolean;
   blockTitle: string;
   blockMessage: string;
   blockReason?: string;
   blockedBy: string;
   blockedAt: Date;
   unblockedBy?: string;
   unblockedAt?: Date;
}

export interface CreateUserBlockData {
   userId: string;
   blockTitle: string;
   blockMessage: string;
   blockReason?: string;
   blockedBy: string;
}

export interface UpdateUserBlockData {
   blockTitle?: string;
   blockMessage?: string;
   blockReason?: string;
   isBlocked?: boolean;
   unblockedBy?: ObjectId;
}

// Pre-built block message templates
export const BLOCK_MESSAGE_TEMPLATES = {
   SUSPICIOUS_LOGIN: {
      title: "Account Temporarily Suspended",
      message:
         "Your account has been temporarily suspended due to suspicious login attempts. Please contact support to verify your identity and restore access to your account.",
      reason: "Suspicious login activity detected",
   },
   SECURITY_REVIEW: {
      title: "Security Review in Progress",
      message:
         "Your account is currently under security review. This is a routine process to ensure the safety of your account. We will notify you once the review is complete.",
      reason: "Routine security review",
   },
   ACCOUNT_SUSPENDED: {
      title: "Account Suspended",
      message:
         "Your account has been suspended due to a violation of our terms of service. Please contact our support team for more information about restoring your account.",
      reason: "Terms of service violation",
   },
   FRAUD_INVESTIGATION: {
      title: "Fraud Investigation",
      message:
         "Your account has been temporarily restricted while we investigate potential fraudulent activity. This is a precautionary measure to protect your account and funds.",
      reason: "Potential fraud investigation",
   },
   COMPLIANCE_REVIEW: {
      title: "Compliance Review Required",
      message:
         "Your account requires additional compliance verification. Please contact our support team to complete the required documentation and restore full access.",
      reason: "Compliance verification required",
   },
   MAINTENANCE: {
      title: "Account Maintenance",
      message:
         "Your account is temporarily unavailable due to system maintenance. We apologize for the inconvenience and expect to restore access shortly.",
      reason: "System maintenance",
   },
} as const;

export type BlockTemplate = keyof typeof BLOCK_MESSAGE_TEMPLATES;
