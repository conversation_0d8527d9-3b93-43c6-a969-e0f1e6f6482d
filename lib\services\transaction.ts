import {
   CreateTransactionData,
   Transaction,
   TransactionDetails,
   TransactionFilters,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { generateTransactionId } from "@/lib/utils/auth";
import { ObjectId, OptionalId } from "mongodb";
import { updateAccountBalance } from "./account";
import { createUserNotification } from "./user-notification";

/**
 * Create a new transaction
 */
export async function createTransaction(data: CreateTransactionData): Promise<{
   success: boolean;
   transaction?: TransactionDetails;
   message?: string;
}> {
   try {
      const transactionsCollection = await getCollection<Transaction>(
         "transactions"
      );

      // For pending transactions, don't update balance yet
      // Only update balance for completed transactions
      let balanceAfter = 0;

      if (data.status === "completed") {
         // Update account balance for completed transactions
         const balanceResult = await updateAccountBalance({
            accountId: data.accountId,
            amount: data.amount,
            type: data.type,
            description: data.description,
         });

         if (!balanceResult.success) {
            return { success: false, message: balanceResult.message };
         }

         balanceAfter = balanceResult.newBalance!;
      } else {
         // For pending transactions, get current balance without updating
         const accountsCollection = await getCollection("accounts");
         const account = await accountsCollection.findOne({
            _id: new ObjectId(data.accountId),
         });

         if (!account) {
            return { success: false, message: "Account not found" };
         }

         balanceAfter = account.availableBalance;
      }

      const transactionId = generateTransactionId();

      const metadata = data.metadata
         ? {
              ...data.metadata,
              cardId: data.metadata.cardId
                 ? new ObjectId(data.metadata.cardId)
                 : undefined,
           }
         : undefined;

      const transactionDate = data.customDate || new Date();

      const newTransaction: OptionalId<Transaction> = {
         userId: new ObjectId(data.userId),
         accountId: new ObjectId(data.accountId),
         transactionId,
         type: data.type,
         amount: data.amount,
         currency: data.currency || "USD",
         description: data.description,
         merchant: data.merchant,
         category: data.category,
         status: data.status || "pending", // Default to pending
         reference: data.reference,
         balanceAfter,
         metadata,
         createdAt: transactionDate,
         updatedAt: new Date(),
      };

      const result = await transactionsCollection.insertOne(newTransaction);

      if (result.insertedId) {
         const transactionDetails: TransactionDetails = {
            id: result.insertedId.toString(),
            userId: data.userId,
            accountId: data.accountId,
            transactionId: newTransaction.transactionId,
            type: newTransaction.type,
            amount: newTransaction.amount,
            currency: newTransaction.currency,
            description: newTransaction.description,
            merchant: newTransaction.merchant,
            category: newTransaction.category,
            status: newTransaction.status,
            reference: newTransaction.reference,
            balanceAfter: newTransaction.balanceAfter,
            metadata: newTransaction.metadata
               ? {
                    ...newTransaction.metadata,
                    cardId: newTransaction.metadata.cardId
                       ? newTransaction.metadata.cardId.toString()
                       : undefined,
                 }
               : undefined,
            date: newTransaction.createdAt,
         };

         // Create notification for transaction
         await createTransactionNotification(transactionDetails);

         return { success: true, transaction: transactionDetails };
      }

      return { success: false, message: "Failed to create transaction" };
   } catch (error) {
      console.error("Error creating transaction:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Get transactions with filters
 */
export async function getTransactions(filters: TransactionFilters): Promise<{
   success: boolean;
   transactions?: TransactionDetails[];
   message?: string;
}> {
   try {
      const transactionsCollection = await getCollection<Transaction>(
         "transactions"
      );

      // Build query
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const query: any = { userId: new ObjectId(filters.userId) };

      if (filters.accountId) {
         query.accountId = new ObjectId(filters.accountId);
      }

      if (filters.type) {
         query.type = filters.type;
      }

      if (filters.category) {
         query.category = filters.category;
      }

      if (filters.status) {
         query.status = filters.status;
      }

      if (filters.dateFrom || filters.dateTo) {
         query.createdAt = {};
         if (filters.dateFrom) {
            query.createdAt.$gte = filters.dateFrom;
         }
         if (filters.dateTo) {
            query.createdAt.$lte = filters.dateTo;
         }
      }

      // Execute query with pagination
      const transactions = await transactionsCollection
         .find(query)
         .sort({ createdAt: -1 })
         .limit(filters.limit || 50)
         .skip(filters.offset || 0)
         .toArray();

      const transactionDetails: TransactionDetails[] = transactions.map(
         (transaction) => ({
            id: transaction._id!.toString(),
            userId: filters.userId,
            accountId: transaction.accountId.toString(),
            transactionId: transaction.transactionId,
            type: transaction.type,
            amount: transaction.amount,
            currency: transaction.currency,
            description: transaction.description,
            merchant: transaction.merchant,
            category: transaction.category,
            status: transaction.status,
            reference: transaction.reference,
            balanceAfter: transaction.balanceAfter,
            metadata: transaction.metadata
               ? {
                    ...transaction.metadata,
                    cardId: transaction.metadata.cardId
                       ? transaction.metadata.cardId.toString()
                       : undefined,
                 }
               : undefined,
            date: transaction.createdAt,
         })
      );

      return { success: true, transactions: transactionDetails };
   } catch (error) {
      console.error("Error getting transactions:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Update transaction status and handle balance changes
 */
export async function updateTransactionStatusWithBalance(
   transactionId: string,
   newStatus: "pending" | "completed" | "failed" | "cancelled"
): Promise<{
   success: boolean;
   transaction?: TransactionDetails;
   message?: string;
}> {
   try {
      const transactionsCollection = await getCollection<Transaction>(
         "transactions"
      );

      const transaction = await transactionsCollection.findOne({
         _id: new ObjectId(transactionId),
      });

      if (!transaction) {
         return { success: false, message: "Transaction not found" };
      }

      const oldStatus = transaction.status;
      let balanceAfter = transaction.balanceAfter;

      // Handle balance changes based on status transition
      if (oldStatus === "pending" && newStatus === "completed") {
         // Apply the transaction to the balance
         const balanceResult = await updateAccountBalance({
            accountId: transaction.accountId.toString(),
            amount: transaction.amount,
            type: transaction.type,
            description: transaction.description,
         });

         if (!balanceResult.success) {
            return { success: false, message: balanceResult.message };
         }

         balanceAfter = balanceResult.newBalance!;
      } else if (
         oldStatus === "completed" &&
         (newStatus === "failed" || newStatus === "cancelled")
      ) {
         // Reverse the transaction from the balance
         const reverseType = transaction.type === "credit" ? "debit" : "credit";
         const balanceResult = await updateAccountBalance({
            accountId: transaction.accountId.toString(),
            amount: transaction.amount,
            type: reverseType,
            description: `Reversal: ${transaction.description}`,
         });

         if (!balanceResult.success) {
            return { success: false, message: balanceResult.message };
         }

         balanceAfter = balanceResult.newBalance!;
      }

      // Update the transaction
      const result = await transactionsCollection.findOneAndUpdate(
         { _id: new ObjectId(transactionId) },
         {
            $set: {
               status: newStatus,
               balanceAfter,
               updatedAt: new Date(),
            },
         },
         { returnDocument: "after" }
      );

      if (!result) {
         return { success: false, message: "Failed to update transaction" };
      }

      const transactionDetails: TransactionDetails = {
         id: result._id!.toString(),
         userId: result.userId.toString(),
         accountId: result.accountId.toString(),
         transactionId: result.transactionId,
         type: result.type,
         amount: result.amount,
         currency: result.currency,
         description: result.description,
         merchant: result.merchant,
         category: result.category,
         status: result.status,
         reference: result.reference,
         balanceAfter: result.balanceAfter,
         metadata: result.metadata
            ? {
                 ...result.metadata,
                 cardId: result.metadata.cardId?.toString(),
              }
            : undefined,
         date: result.createdAt,
      };

      // Create notification for status change
      if (oldStatus !== newStatus) {
         await createTransactionNotification(transactionDetails);
      }

      return { success: true, transaction: transactionDetails };
   } catch (error) {
      console.error("Error updating transaction status with balance:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Get recent transactions for a user
 */
export async function getRecentTransactions(
   userId: string,
   limit: number = 10
): Promise<{
   success: boolean;
   transactions?: TransactionDetails[];
   message?: string;
}> {
   return getTransactions({
      userId,
      limit,
      offset: 0,
   });
}

/**
 * Get transaction by ID
 */
export async function getTransactionById(transactionId: string): Promise<{
   success: boolean;
   transaction?: TransactionDetails;
   message?: string;
}> {
   try {
      const transactionsCollection = await getCollection<Transaction>(
         "transactions"
      );
      const transaction = await transactionsCollection.findOne({
         _id: new ObjectId(transactionId),
      });

      if (!transaction) {
         return { success: false, message: "Transaction not found" };
      }

      const transactionDetails: TransactionDetails = {
         id: transaction._id!.toString(),
         userId: transaction.userId.toString(),
         accountId: transaction.accountId.toString(),
         transactionId: transaction.transactionId,
         type: transaction.type,
         amount: transaction.amount,
         currency: transaction.currency,
         description: transaction.description,
         merchant: transaction.merchant,
         category: transaction.category,
         status: transaction.status,
         reference: transaction.reference,
         balanceAfter: transaction.balanceAfter,
         metadata: transaction.metadata
            ? {
                 ...transaction.metadata,
                 cardId: transaction.metadata.cardId
                    ? transaction.metadata.cardId.toString()
                    : undefined,
              }
            : undefined,
         date: transaction.createdAt,
      };

      return { success: true, transaction: transactionDetails };
   } catch (error) {
      console.error("Error getting transaction:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Get transaction statistics for a user
 */
export async function getTransactionStats(
   userId: string,
   dateFrom?: Date,
   dateTo?: Date
): Promise<{
   success: boolean;
   stats?: {
      totalIncome: number;
      totalExpenses: number;
      transactionCount: number;
      categories: { [key: string]: number };
   };
   message?: string;
}> {
   try {
      const transactionsCollection = await getCollection<Transaction>(
         "transactions"
      );

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const query: any = { userId: new ObjectId(userId) };

      if (dateFrom || dateTo) {
         query.createdAt = {};
         if (dateFrom) query.createdAt.$gte = dateFrom;
         if (dateTo) query.createdAt.$lte = dateTo;
      }

      const transactions = await transactionsCollection.find(query).toArray();

      let totalIncome = 0;
      let totalExpenses = 0;
      const categories: { [key: string]: number } = {};

      transactions.forEach((transaction) => {
         if (transaction.type === "credit") {
            totalIncome += transaction.amount;
         } else {
            totalExpenses += transaction.amount;
         }

         if (transaction.category) {
            categories[transaction.category] =
               (categories[transaction.category] || 0) + transaction.amount;
         }
      });

      return {
         success: true,
         stats: {
            totalIncome,
            totalExpenses,
            transactionCount: transactions.length,
            categories,
         },
      };
   } catch (error) {
      console.error("Error getting transaction stats:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Create notification for transaction
 */
async function createTransactionNotification(
   transaction: TransactionDetails
): Promise<void> {
   try {
      let title: string;
      let message: string;
      let type: "info" | "success" | "warning" | "error";

      switch (transaction.status) {
         case "completed":
            if (transaction.type === "credit") {
               title = "Payment Received";
               message = `You received $${transaction.amount.toFixed(2)}${
                  transaction.merchant ? ` from ${transaction.merchant}` : ""
               }`;
               type = "success";
            } else {
               title = "Payment Sent";
               message = `You sent $${transaction.amount.toFixed(2)}${
                  transaction.merchant ? ` to ${transaction.merchant}` : ""
               }`;
               type = "success";
            }
            break;
         case "pending":
            title = "Transaction Pending";
            message = `Your transaction of $${transaction.amount.toFixed(
               2
            )} is pending approval`;
            type = "info";
            break;
         case "failed":
            title = "Transaction Failed";
            message = `Your transaction of $${transaction.amount.toFixed(
               2
            )} failed. Please try again.`;
            type = "error";
            break;
         case "cancelled":
            title = "Transaction Cancelled";
            message = `Your transaction of $${transaction.amount.toFixed(
               2
            )} was cancelled`;
            type = "warning";
            break;
         default:
            return; // Don't create notification for unknown status
      }

      await createUserNotification({
         userId: transaction.userId,
         title,
         message,
         type,
         category: "transaction",
         metadata: {
            transactionId: transaction.id,
            accountId: transaction.accountId,
            amount: transaction.amount,
            recipientName: transaction.merchant,
         },
      });
   } catch (error) {
      console.error("Error creating transaction notification:", error);
      // Don't throw error as this is not critical for transaction creation
   }
}
