import { Document, ObjectId } from "mongodb";

export interface User extends Document {
   _id?: ObjectId;
   email: string;
   password: string; // bcrypt hashed
   firstName: string;
   lastName: string;
   username: string;
   phoneNumber: string;
   country: string;
   accountType: string;
   role: "user" | "admin";
   verificationStatus: "verified" | "pending" | "unverified";
   transactionPin: string; // bcrypt hashed 4-digit PIN
   createdAt: Date;
   updatedAt: Date;
}

export interface UserProfile {
   id: string;
   email: string;
   firstName: string;
   lastName: string;
   username: string;
   phoneNumber: string;
   country: string;
   accountType: string;
   role: "user" | "admin";
   verificationStatus: "verified" | "pending" | "unverified";
}

export interface CreateUserData {
   email: string;
   password: string;
   firstName: string;
   lastName: string;
   username: string;
   phoneNumber: string;
   country: string;
   accountType: string;
   role?: "user" | "admin";
   transactionPin: string;
}

export interface LoginCredentials {
   email: string;
   password: string;
}

export interface AuthResponse {
   success: boolean;
   user?: UserProfile;
   message?: string;
}
