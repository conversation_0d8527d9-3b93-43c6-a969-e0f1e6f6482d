"use client";

import {
   createNewTransaction,
   getRecentTransactionHistory,
   getTransaction,
   getTransactionHistory,
   getTransactionStatistics,
   receiveMoney,
   sendMoney,
} from "@/lib/actions";
import { CreateTransactionData, TransactionFilters } from "@/lib/models";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

/**
 * Hook to get transaction history with filters
 */
export function useTransactionHistory(filters: TransactionFilters) {
   return useQuery({
      queryKey: ["transactions", "history", filters],
      queryFn: async () => {
         const result = await getTransactionHistory(filters);
         if (!result.success) {
            throw new Error(
               result.message || "Failed to get transaction history"
            );
         }
         return result.transactions || [];
      },
      enabled: !!filters.userId,
      staleTime: 1 * 60 * 1000, // 1 minute
   });
}

/**
 * Hook to get recent transactions
 */
export function useRecentTransactions(
   userId: string | undefined,
   limit?: number
) {
   return useQuery({
      queryKey: ["transactions", "recent", userId, limit],
      queryFn: async () => {
         if (!userId) throw new Error("User ID is required");
         const result = await getRecentTransactionHistory(userId, limit);
         if (!result.success) {
            throw new Error(
               result.message || "Failed to get recent transactions"
            );
         }
         return result.transactions || [];
      },
      enabled: !!userId,
      staleTime: 30 * 1000, // 30 seconds
   });
}

/**
 * Hook to get a specific transaction
 */
export function useTransaction(transactionId: string | undefined) {
   return useQuery({
      queryKey: ["transaction", transactionId],
      queryFn: async () => {
         if (!transactionId) throw new Error("Transaction ID is required");
         const result = await getTransaction(transactionId);
         if (!result.success) {
            throw new Error(result.message || "Failed to get transaction");
         }
         return result.transaction!;
      },
      enabled: !!transactionId,
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to get transaction statistics
 */
export function useTransactionStatistics(
   userId: string | undefined,
   dateFrom?: Date,
   dateTo?: Date
) {
   return useQuery({
      queryKey: ["transactions", "stats", userId, dateFrom, dateTo],
      queryFn: async () => {
         if (!userId) throw new Error("User ID is required");
         const result = await getTransactionStatistics(
            userId,
            dateFrom,
            dateTo
         );
         if (!result.success) {
            throw new Error(
               result.message || "Failed to get transaction statistics"
            );
         }
         return result.stats!;
      },
      enabled: !!userId,
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to create a new transaction
 */
export function useCreateTransaction() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async (data: CreateTransactionData) => {
         const result = await createNewTransaction(data);
         if (!result.success) {
            throw new Error(result.message || "Failed to create transaction");
         }
         return result.transaction!;
      },
      onSuccess: () => {
         // Invalidate related queries
         queryClient.invalidateQueries({ queryKey: ["transactions"] });
         queryClient.invalidateQueries({ queryKey: ["account"] });
         queryClient.invalidateQueries({ queryKey: ["accounts"] });
      },
   });
}

/**
 * Hook to send money
 */
export function useSendMoney() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async (data: {
         userId: string;
         accountId: string;
         amount: number;
         recipientAccount: string;
         recipientName: string;
         description: string;
         transferType: "internal" | "external" | "international";
      }) => {
         const result = await sendMoney(data);
         if (!result.success) {
            throw new Error(result.message || "Failed to send money");
         }
         return result.transaction!;
      },
      onSuccess: () => {
         // Invalidate related queries
         queryClient.invalidateQueries({ queryKey: ["transactions"] });
         queryClient.invalidateQueries({ queryKey: ["account"] });
         queryClient.invalidateQueries({ queryKey: ["accounts"] });
      },
   });
}

/**
 * Hook to receive money
 */
export function useReceiveMoney() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async (data: {
         userId: string;
         accountId: string;
         amount: number;
         senderName: string;
         description: string;
      }) => {
         const result = await receiveMoney(data);
         if (!result.success) {
            throw new Error(result.message || "Failed to receive money");
         }
         return result.transaction!;
      },
      onSuccess: () => {
         // Invalidate related queries
         queryClient.invalidateQueries({ queryKey: ["transactions"] });
         queryClient.invalidateQueries({ queryKey: ["account"] });
         queryClient.invalidateQueries({ queryKey: ["accounts"] });
      },
   });
}
