"use server";

import {
  CreateUserNotificationData,
  NotificationFilters,
  UserNotificationDetails,
} from "@/lib/models";
import {
  cleanupExpiredNotifications,
  createGlobalNotification,
  createUserNotification,
  deleteNotification,
  getUnreadNotificationCount,
  getUserNotifications,
  markAllNotificationsAsRead,
  markNotificationAsRead,
} from "@/lib/services/user-notification";
import { revalidatePath } from "next/cache";

// ============================================================================
// USER NOTIFICATION ACTIONS
// ============================================================================

/**
 * Create a new user notification
 */
export async function createUserNotificationAction(
  data: CreateUserNotificationData
): Promise<{
  success: boolean;
  notification?: UserNotificationDetails;
  message?: string;
}> {
  try {
    const result = await createUserNotification(data);
    
    if (result.success) {
      // Revalidate relevant paths
      revalidatePath("/dashboard");
      revalidatePath("/admin");
      if (data.userId) {
        revalidatePath(`/dashboard/notifications`);
      }
    }
    
    return result;
  } catch (error) {
    console.error("Error in createUserNotificationAction:", error);
    return {
      success: false,
      message: "Failed to create notification",
    };
  }
}

/**
 * Create global notification for all users
 */
export async function createGlobalNotificationAction(
  data: Omit<CreateUserNotificationData, "userId" | "isGlobal"> & {
    createdBy: string;
  }
): Promise<{
  success: boolean;
  message?: string;
  notificationsCreated?: number;
}> {
  try {
    const result = await createGlobalNotification(data);
    
    if (result.success) {
      // Revalidate all dashboard paths since this affects all users
      revalidatePath("/dashboard");
      revalidatePath("/admin");
    }
    
    return result;
  } catch (error) {
    console.error("Error in createGlobalNotificationAction:", error);
    return {
      success: false,
      message: "Failed to create global notifications",
    };
  }
}

/**
 * Get user notifications with filtering
 */
export async function getUserNotificationsAction(
  filters: NotificationFilters
): Promise<{
  success: boolean;
  notifications?: UserNotificationDetails[];
  total?: number;
  message?: string;
}> {
  try {
    return await getUserNotifications(filters);
  } catch (error) {
    console.error("Error in getUserNotificationsAction:", error);
    return {
      success: false,
      message: "Failed to get notifications",
    };
  }
}

/**
 * Mark notification as read
 */
export async function markNotificationAsReadAction(
  notificationId: string
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    const result = await markNotificationAsRead(notificationId);
    
    if (result.success) {
      revalidatePath("/dashboard");
      revalidatePath("/dashboard/notifications");
    }
    
    return result;
  } catch (error) {
    console.error("Error in markNotificationAsReadAction:", error);
    return {
      success: false,
      message: "Failed to mark notification as read",
    };
  }
}

/**
 * Mark all notifications as read for a user
 */
export async function markAllNotificationsAsReadAction(
  userId: string
): Promise<{
  success: boolean;
  message?: string;
  updatedCount?: number;
}> {
  try {
    const result = await markAllNotificationsAsRead(userId);
    
    if (result.success) {
      revalidatePath("/dashboard");
      revalidatePath("/dashboard/notifications");
    }
    
    return result;
  } catch (error) {
    console.error("Error in markAllNotificationsAsReadAction:", error);
    return {
      success: false,
      message: "Failed to mark all notifications as read",
    };
  }
}

/**
 * Delete notification
 */
export async function deleteNotificationAction(
  notificationId: string
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    const result = await deleteNotification(notificationId);
    
    if (result.success) {
      revalidatePath("/dashboard");
      revalidatePath("/dashboard/notifications");
    }
    
    return result;
  } catch (error) {
    console.error("Error in deleteNotificationAction:", error);
    return {
      success: false,
      message: "Failed to delete notification",
    };
  }
}

/**
 * Get unread notification count for a user
 */
export async function getUnreadNotificationCountAction(
  userId: string
): Promise<{
  success: boolean;
  count?: number;
  message?: string;
}> {
  try {
    return await getUnreadNotificationCount(userId);
  } catch (error) {
    console.error("Error in getUnreadNotificationCountAction:", error);
    return {
      success: false,
      message: "Failed to get unread notification count",
    };
  }
}

/**
 * Clean up expired notifications (admin action)
 */
export async function cleanupExpiredNotificationsAction(): Promise<{
  success: boolean;
  deletedCount?: number;
  message?: string;
}> {
  try {
    const result = await cleanupExpiredNotifications();
    
    if (result.success) {
      revalidatePath("/dashboard");
      revalidatePath("/admin");
    }
    
    return result;
  } catch (error) {
    console.error("Error in cleanupExpiredNotificationsAction:", error);
    return {
      success: false,
      message: "Failed to clean up expired notifications",
    };
  }
}
