"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { useCards } from "@/hooks/use-admin";
import { motion } from "framer-motion";
import {
   CheckCircle,
   Clock,
   CreditCard,
   Filter,
   Search,
   XCircle,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function AdminCards() {
   const [searchTerm, setSearchTerm] = useState("");
   const [statusFilter, setStatusFilter] = useState<
      "all" | "active" | "inactive" | "blocked" | "expired"
   >("all");
   const [tierFilter, setTierFilter] = useState<
      "all" | "standard" | "gold" | "platinum" | "black"
   >("all");

   // Get cards from database
   const { data: cardsData } = useCards({
      page: 1,
      limit: 100,
      status: statusFilter === "all" ? undefined : statusFilter,
      cardTier: tierFilter === "all" ? undefined : tierFilter,
   });

   const allCards = cardsData?.success ? cardsData.cards || [] : [];

   const filteredCards = allCards.filter((card) => {
      const matchesSearch =
         searchTerm === "" ||
         card.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
         card.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
         card.cardNumber.includes(searchTerm) ||
         card.id.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus =
         statusFilter === "all" || card.status === statusFilter;
      const matchesTier = tierFilter === "all" || card.cardTier === tierFilter;

      return matchesSearch && matchesStatus && matchesTier;
   });

   // const handleApproveCard = (cardId: string) => {
   //    toast.success(`Card ${cardId} approved successfully`);
   //    // In real app, this would call an API
   // };

   const handleBlockCard = (cardId: string) => {
      toast.success(`Card ${cardId} blocked successfully`);
      // In real app, this would call an API
   };

   const handleUnblockCard = (cardId: string) => {
      toast.success(`Card ${cardId} unblocked successfully`);
      // In real app, this would call an API
   };

   const getStatusColor = (status: string) => {
      switch (status) {
         case "active":
            return "bg-success text-success-foreground";
         case "pending":
            return "bg-warning text-warning-foreground";
         case "blocked":
            return "bg-destructive text-destructive-foreground";
         case "expired":
            return "bg-muted text-muted-foreground";
         default:
            return "bg-muted text-muted-foreground";
      }
   };

   const getTierColor = (tier: string) => {
      switch (tier) {
         case "standard":
            return "bg-blue-100 text-blue-800";
         case "gold":
            return "bg-yellow-100 text-yellow-800";
         case "platinum":
            return "bg-gray-100 text-gray-800";
         case "black":
            return "bg-gray-900 text-white";
         default:
            return "bg-gray-100 text-gray-800";
      }
   };

   const cardStats = {
      total: allCards.length,
      active: allCards.filter((c) => c.status === "active").length,
      inactive: allCards.filter((c) => c.status === "inactive").length,
      blocked: allCards.filter((c) => c.status === "blocked").length,
   };

   return (
      <div className="container mx-auto px-4 py-8">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
         >
            {/* Header */}
            <div className="mb-8">
               <div className="flex items-center justify-between">
                  <div>
                     <h1 className="text-3xl font-bold text-foreground mb-2">
                        Card Management
                     </h1>
                     <p className="text-muted-foreground">
                        Manage user cards, applications, and card statuses.
                     </p>
                  </div>
                  <div className="flex items-center gap-2">
                     <CreditCard className="h-5 w-5 text-primary" />
                     <span className="text-sm font-medium">
                        {cardStats.total} total cards
                     </span>
                  </div>
               </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
               <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                     <CardTitle className="text-sm font-medium">
                        Total Cards
                     </CardTitle>
                     <CreditCard className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                     <div className="text-2xl font-bold">{cardStats.total}</div>
                  </CardContent>
               </Card>
               <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                     <CardTitle className="text-sm font-medium">
                        Active Cards
                     </CardTitle>
                     <CheckCircle className="h-4 w-4 text-success" />
                  </CardHeader>
                  <CardContent>
                     <div className="text-2xl font-bold text-success">
                        {cardStats.active}
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                     <CardTitle className="text-sm font-medium">
                        Inactive
                     </CardTitle>
                     <Clock className="h-4 w-4 text-warning" />
                  </CardHeader>
                  <CardContent>
                     <div className="text-2xl font-bold text-warning">
                        {cardStats.inactive}
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                     <CardTitle className="text-sm font-medium">
                        Blocked
                     </CardTitle>
                     <XCircle className="h-4 w-4 text-destructive" />
                  </CardHeader>
                  <CardContent>
                     <div className="text-2xl font-bold text-destructive">
                        {cardStats.blocked}
                     </div>
                  </CardContent>
               </Card>
            </div>

            {/* Filters */}
            <Card className="mb-6">
               <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                     <Filter className="h-5 w-5" />
                     Filters
                  </CardTitle>
               </CardHeader>
               <CardContent>
                  <div className="flex flex-col md:flex-row gap-4">
                     <div className="flex-1">
                        <div className="relative">
                           <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                           <Input
                              placeholder="Search by name, email, card number, or ID..."
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="pl-10"
                           />
                        </div>
                     </div>
                     <Select
                        value={statusFilter}
                        onValueChange={(value: typeof statusFilter) =>
                           setStatusFilter(value)
                        }
                     >
                        <SelectTrigger className="w-[180px]">
                           <SelectValue placeholder="Card Status" />
                        </SelectTrigger>
                        <SelectContent>
                           <SelectItem value="all">All Statuses</SelectItem>
                           <SelectItem value="active">Active</SelectItem>
                           <SelectItem value="inactive">Inactive</SelectItem>
                           <SelectItem value="blocked">Blocked</SelectItem>
                           <SelectItem value="expired">Expired</SelectItem>
                        </SelectContent>
                     </Select>
                     <Select
                        value={tierFilter}
                        onValueChange={(value: typeof tierFilter) =>
                           setTierFilter(value)
                        }
                     >
                        <SelectTrigger className="w-[180px]">
                           <SelectValue placeholder="Card Tier" />
                        </SelectTrigger>
                        <SelectContent>
                           <SelectItem value="all">All Tiers</SelectItem>
                           <SelectItem value="standard">Standard</SelectItem>
                           <SelectItem value="gold">Gold</SelectItem>
                           <SelectItem value="platinum">Platinum</SelectItem>
                           <SelectItem value="black">Black</SelectItem>
                        </SelectContent>
                     </Select>
                  </div>
               </CardContent>
            </Card>

            {/* Cards List */}
            <div className="space-y-4">
               {filteredCards.length === 0 ? (
                  <Card>
                     <CardContent className="p-8 text-center">
                        <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-semibold mb-2">
                           No Cards Found
                        </h3>
                        <p className="text-muted-foreground">
                           No cards match your current filters.
                        </p>
                     </CardContent>
                  </Card>
               ) : (
                  filteredCards.map((card) => (
                     <Card
                        key={card.id}
                        className="hover:shadow-md transition-shadow"
                     >
                        <CardContent className="p-6">
                           <div className="flex items-center justify-between">
                              <div className="flex-1">
                                 <div className="flex items-center gap-3 mb-2">
                                    <CreditCard className="h-5 w-5 text-muted-foreground" />
                                    <span className="font-mono text-sm">
                                       {card.id}
                                    </span>
                                    <Badge
                                       className={getStatusColor(card.status)}
                                    >
                                       {card.status}
                                    </Badge>
                                    <Badge
                                       className={getTierColor(card.cardTier)}
                                    >
                                       {card.cardTier}
                                    </Badge>
                                 </div>
                                 <h3 className="font-semibold text-lg mb-1">
                                    {card.userName}
                                 </h3>
                                 <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-muted-foreground">
                                    <div>
                                       <span className="font-medium">
                                          Email:
                                       </span>{" "}
                                       {card.userEmail}
                                    </div>
                                    <div>
                                       <span className="font-medium">
                                          Card:
                                       </span>{" "}
                                       {card.cardNumber}
                                    </div>
                                    <div>
                                       <span className="font-medium">
                                          Daily Limit:
                                       </span>{" "}
                                       ${card.dailyLimit.toLocaleString()}
                                    </div>
                                    <div>
                                       <span className="font-medium">
                                          Monthly Limit:
                                       </span>{" "}
                                       ${card.monthlyLimit.toLocaleString()}
                                    </div>
                                 </div>
                              </div>
                              <div className="flex items-center gap-2">
                                 {card.status === "active" && (
                                    <Button
                                       onClick={() => handleBlockCard(card.id)}
                                       size="sm"
                                       variant="destructive"
                                    >
                                       <XCircle className="h-4 w-4 mr-1" />
                                       Block
                                    </Button>
                                 )}
                                 {card.status === "blocked" && (
                                    <Button
                                       onClick={() =>
                                          handleUnblockCard(card.id)
                                       }
                                       size="sm"
                                       className="bg-success hover:bg-success/90"
                                    >
                                       <CheckCircle className="h-4 w-4 mr-1" />
                                       Unblock
                                    </Button>
                                 )}
                                 {card.status === "blocked" && (
                                    <Button
                                       onClick={() =>
                                          handleUnblockCard(card.id)
                                       }
                                       size="sm"
                                       className="bg-success hover:bg-success/90"
                                    >
                                       <CheckCircle className="h-4 w-4 mr-1" />
                                       Unblock
                                    </Button>
                                 )}
                              </div>
                           </div>
                        </CardContent>
                     </Card>
                  ))
               )}
            </div>
         </motion.div>
      </div>
   );
}
