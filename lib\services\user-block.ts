/* eslint-disable @typescript-eslint/no-explicit-any */
import {
   CreateUserBlockData,
   UpdateUserBlockData,
   UserBlock,
   UserBlockDetails,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { ObjectId, OptionalId } from "mongodb";

/**
 * Get user block status
 */
export async function getUserBlockStatus(
   userId: string
): Promise<{ success: boolean; block?: UserBlockDetails; message?: string }> {
   try {
      const blocksCollection = await getCollection<UserBlock>("user_blocks");

      const block = await blocksCollection.findOne({
         userId: new ObjectId(userId),
         isBlocked: true,
      });

      if (!block) {
         return { success: true, block: undefined };
      }

      const blockDetails: UserBlockDetails = {
         id: block._id!.toString(),
         userId: block.userId.toString(),
         isBlocked: block.isBlocked,
         blockTitle: block.blockTitle,
         blockMessage: block.blockMessage,
         blockReason: block.blockReason,
         blockedBy: block.blockedBy.toString(),
         blockedAt: block.blockedAt,
         unblockedBy: block.unblockedBy?.toString(),
         unblockedAt: block.unblockedAt,
      };

      return { success: true, block: blockDetails };
   } catch (error) {
      console.error("Error getting user block status:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Block a user
 */
export async function blockUser(
   data: CreateUserBlockData
): Promise<{ success: boolean; block?: UserBlockDetails; message?: string }> {
   try {
      const blocksCollection = await getCollection<UserBlock>("user_blocks");

      // Check if user is already blocked
      const existingBlock = await blocksCollection.findOne({
         userId: new ObjectId(data.userId),
         isBlocked: true,
      });

      if (existingBlock) {
         return { success: false, message: "User is already blocked" };
      }

      const newBlock: OptionalId<UserBlock> = {
         userId: new ObjectId(data.userId),
         isBlocked: true,
         blockTitle: data.blockTitle,
         blockMessage: data.blockMessage,
         blockReason: data.blockReason,
         blockedBy: new ObjectId(data.blockedBy),
         blockedAt: new Date(),
         createdAt: new Date(),
         updatedAt: new Date(),
      };

      const result = await blocksCollection.insertOne(newBlock);

      if (result.insertedId) {
         const blockDetails: UserBlockDetails = {
            id: result.insertedId.toString(),
            userId: data.userId,
            isBlocked: true,
            blockTitle: data.blockTitle,
            blockMessage: data.blockMessage,
            blockReason: data.blockReason,
            blockedBy: data.blockedBy,
            blockedAt: new Date(),
         };

         return { success: true, block: blockDetails };
      }

      return { success: false, message: "Failed to block user" };
   } catch (error) {
      console.error("Error blocking user:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Unblock a user
 */
export async function unblockUser(
   userId: string,
   unblockedBy: string
): Promise<{ success: boolean; message?: string }> {
   try {
      const blocksCollection = await getCollection<UserBlock>("user_blocks");

      const result = await blocksCollection.findOneAndUpdate(
         {
            userId: new ObjectId(userId),
            isBlocked: true,
         },
         {
            $set: {
               isBlocked: false,
               unblockedBy: new ObjectId(unblockedBy),
               unblockedAt: new Date(),
               updatedAt: new Date(),
            },
         },
         { returnDocument: "after" }
      );

      if (!result) {
         return { success: false, message: "User is not currently blocked" };
      }

      return { success: true };
   } catch (error) {
      console.error("Error unblocking user:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Update user block
 */
export async function updateUserBlock(
   userId: string,
   data: UpdateUserBlockData
): Promise<{ success: boolean; block?: UserBlockDetails; message?: string }> {
   try {
      const blocksCollection = await getCollection<UserBlock>("user_blocks");

      const updateData = {
         ...data,
         updatedAt: new Date(),
      };

      if (data.unblockedBy && data.isBlocked === false) {
         updateData.unblockedBy = data.unblockedBy;
      }

      const result = await blocksCollection.findOneAndUpdate(
         {
            userId: new ObjectId(userId),
            isBlocked: true,
         },
         {
            $set: updateData,
         },
         { returnDocument: "after" }
      );

      if (!result) {
         return { success: false, message: "User block not found" };
      }

      const blockDetails: UserBlockDetails = {
         id: result._id!.toString(),
         userId: result.userId.toString(),
         isBlocked: result.isBlocked,
         blockTitle: result.blockTitle,
         blockMessage: result.blockMessage,
         blockReason: result.blockReason,
         blockedBy: result.blockedBy.toString(),
         blockedAt: result.blockedAt,
         unblockedBy: result.unblockedBy?.toString(),
         unblockedAt: result.unblockedAt,
      };

      return { success: true, block: blockDetails };
   } catch (error) {
      console.error("Error updating user block:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Get all blocked users for admin
 */
export async function getAllBlockedUsers(options: {
   page?: number;
   limit?: number;
   search?: string;
}): Promise<{
   success: boolean;
   blocks?: any[];
   total?: number;
   message?: string;
}> {
   try {
      const blocksCollection = await getCollection<UserBlock>("user_blocks");

      const { page = 1, limit = 20, search } = options;
      const skip = (page - 1) * limit;

      // Build aggregation pipeline
      const pipeline: any[] = [
         { $match: { isBlocked: true } },
         {
            $lookup: {
               from: "users",
               localField: "userId",
               foreignField: "_id",
               as: "user",
            },
         },
         {
            $unwind: "$user",
         },
      ];

      // Add search filter if provided
      if (search) {
         pipeline.push({
            $match: {
               $or: [
                  { "user.firstName": { $regex: search, $options: "i" } },
                  { "user.lastName": { $regex: search, $options: "i" } },
                  { "user.email": { $regex: search, $options: "i" } },
                  { blockTitle: { $regex: search, $options: "i" } },
                  { blockReason: { $regex: search, $options: "i" } },
               ],
            },
         });
      }

      // Add pagination
      pipeline.push({ $skip: skip }, { $limit: limit });

      const results = await blocksCollection.aggregate(pipeline).toArray();

      // Get total count for pagination
      const countPipeline = [...pipeline];
      countPipeline.pop(); // Remove limit
      countPipeline.pop(); // Remove skip
      countPipeline.push({ $count: "total" });

      const countResult = await blocksCollection
         .aggregate(countPipeline)
         .toArray();
      const total = countResult.length > 0 ? countResult[0].total : 0;

      const blockedUsers = results.map((result) => ({
         id: result._id.toString(),
         userId: result.userId.toString(),
         user: {
            firstName: result.user.firstName,
            lastName: result.user.lastName,
            email: result.user.email,
         },
         isBlocked: result.isBlocked,
         blockTitle: result.blockTitle,
         blockMessage: result.blockMessage,
         blockReason: result.blockReason,
         blockedAt: result.blockedAt,
      }));

      return { success: true, blocks: blockedUsers, total };
   } catch (error) {
      console.error("Error getting all blocked users:", error);
      return { success: false, message: "Internal server error" };
   }
}
