"use client";

import { But<PERSON> } from "@/components/ui/button";
import { DashboardBreadcrumb } from "@/components/ui/dashboard-breadcrumb";
import { TransactionStatusBadge } from "@/components/ui/transaction-status-badge";
import { useAuth, useTransactionHistory } from "@/hooks";
import { motion } from "framer-motion";
import {
   ArrowDown,
   ArrowUp,
   Coffee,
   Download,
   History,
   Home,
   Search,
   ShoppingCart,
   Smartphone,
} from "lucide-react";
import { useState } from "react";

export default function AccountHistory() {
   const { user } = useAuth();
   const [filterType, setFilterType] = useState("all");
   const [statusFilter, setStatusFilter] = useState("all");
   const [searchTerm, setSearchTerm] = useState("");

   // Get transaction history from database
   const {
      data: transactionHistory = [],
      isLoading,
      error,
   } = useTransactionHistory({
      userId: user?.id || "",
      type:
         filterType === "all" ? undefined : (filterType as "credit" | "debit"),
      status:
         statusFilter === "all"
            ? undefined
            : (statusFilter as
                 | "pending"
                 | "completed"
                 | "failed"
                 | "cancelled"),
      limit: 50,
      offset: 0,
   });

   // Helper function to get icon for transaction category
   const getCategoryIcon = (category: string) => {
      switch (category.toLowerCase()) {
         case "food & dining":
         case "food":
            return Coffee;
         case "groceries":
         case "shopping":
            return ShoppingCart;
         case "housing":
         case "rent":
            return Home;
         case "electronics":
         case "technology":
            return Smartphone;
         case "transportation":
         case "gas":
            return ArrowUp;
         case "income":
         case "salary":
         case "rewards":
            return ArrowDown;
         default:
            return ArrowUp;
      }
   };

   // Convert database transactions to display format
   const allTransactions = transactionHistory.map((transaction) => ({
      id: transaction.id,
      type: transaction.type,
      amount: transaction.amount,
      description: transaction.description,
      merchant: transaction.merchant || "Unknown",
      date: transaction.date,
      icon: getCategoryIcon(transaction.category),
      category: transaction.category,
      status: transaction.status,
   }));

   // Filter transactions based on search term
   const filteredTransactions = allTransactions.filter((transaction) => {
      const matchesSearch =
         searchTerm === "" ||
         transaction.description
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
         transaction.merchant
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
         transaction.category.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
   });

   const formatDate = (date: Date) => {
      return date.toLocaleDateString("en-US", {
         year: "numeric",
         month: "short",
         day: "numeric",
         hour: "2-digit",
         minute: "2-digit",
      });
   };

   const handleExport = () => {
      alert("Transaction history exported successfully!");
   };

   // Show loading state
   if (isLoading) {
      return (
         <div>
            <DashboardBreadcrumb items={[{ label: "Account History" }]} />
            <div className="container mx-auto px-4 py-8">
               <div className="space-y-4">
                  {[1, 2, 3, 4, 5].map((i) => (
                     <div key={i} className="bg-card rounded-lg p-4 border">
                        <div className="animate-pulse">
                           <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                           <div className="h-3 bg-muted rounded w-1/2"></div>
                        </div>
                     </div>
                  ))}
               </div>
            </div>
         </div>
      );
   }

   // Show error state
   if (error) {
      return (
         <div>
            <DashboardBreadcrumb items={[{ label: "Account History" }]} />
            <div className="container mx-auto px-4 py-8">
               <div className="text-center py-8">
                  <p className="text-destructive">
                     Failed to load transaction history. Please try again.
                  </p>
               </div>
            </div>
         </div>
      );
   }

   return (
      <div>
         {/* Breadcrumbs */}
         <DashboardBreadcrumb items={[{ label: "Account History" }]} />

         {/* Main Content */}
         <div className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
            >
               <div className="flex items-center justify-between mb-8">
                  <div>
                     <h1 className="text-3xl font-bold text-foreground mb-2">
                        Transaction History
                     </h1>
                     <p className="text-muted-foreground">
                        View and manage your account transactions
                     </p>
                  </div>
                  <Button
                     variant="outline"
                     onClick={handleExport}
                     className="flex items-center gap-2"
                  >
                     <Download size={16} />
                     Export
                  </Button>
               </div>

               {/* Filters and Search */}
               <div className="bank-card p-6 mb-6">
                  <div className="flex flex-col md:flex-row gap-4">
                     <div className="flex-1">
                        <div className="relative">
                           <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                           <input
                              type="text"
                              placeholder="Search transactions..."
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground"
                           />
                        </div>
                     </div>
                     <div className="flex gap-2">
                        <select
                           value={filterType}
                           onChange={(e) => setFilterType(e.target.value)}
                           className="px-4 py-2 border border-border rounded-lg bg-background text-foreground"
                        >
                           <option value="all">All Transactions</option>
                           <option value="credit">Credits Only</option>
                           <option value="debit">Debits Only</option>
                        </select>
                        <select
                           value={statusFilter}
                           onChange={(e) => setStatusFilter(e.target.value)}
                           className="px-4 py-2 border border-border rounded-lg bg-background text-foreground"
                        >
                           <option value="all">All Status</option>
                           <option value="completed">Completed</option>
                           <option value="pending">Pending</option>
                           <option value="failed">Failed</option>
                        </select>
                     </div>
                  </div>
               </div>

               {/* Transactions List */}
               <div className="bank-card p-6">
                  <div className="space-y-4">
                     {filteredTransactions.length === 0 ? (
                        <div className="text-center py-8">
                           <History className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                           <p className="text-muted-foreground">
                              No transactions found
                           </p>
                        </div>
                     ) : (
                        filteredTransactions.map((transaction) => {
                           const IconComponent = transaction.icon;
                           return (
                              <div
                                 key={transaction.id}
                                 className="flex items-center justify-between p-4 rounded-lg border border-border hover:bg-muted/30 transition-colors"
                              >
                                 <div className="flex items-center gap-4">
                                    <div
                                       className={`p-2 rounded-lg ${
                                          transaction.type === "credit"
                                             ? "bg-success/10"
                                             : "bg-muted"
                                       }`}
                                    >
                                       <IconComponent
                                          className={`w-5 h-5 ${
                                             transaction.type === "credit"
                                                ? "text-success"
                                                : "text-muted-foreground"
                                          }`}
                                       />
                                    </div>
                                    <div>
                                       <div className="flex items-center gap-2 mb-1">
                                          <p className="font-medium text-foreground">
                                             {transaction.description}
                                          </p>
                                          <TransactionStatusBadge
                                             status={transaction.status}
                                             showIcon={false}
                                             className="text-xs"
                                          />
                                       </div>
                                       <p className="text-sm text-muted-foreground">
                                          {transaction.merchant} •{" "}
                                          {transaction.category}
                                       </p>
                                       <p className="text-xs text-muted-foreground">
                                          {formatDate(transaction.date)}
                                       </p>
                                    </div>
                                 </div>
                                 <div className="text-right">
                                    <p
                                       className={`font-semibold ${
                                          transaction.type === "credit"
                                             ? "text-success"
                                             : "text-foreground"
                                       }`}
                                    >
                                       {transaction.type === "credit"
                                          ? "+"
                                          : "-"}
                                       $
                                       {transaction.amount.toLocaleString(
                                          "en-US",
                                          { minimumFractionDigits: 2 }
                                       )}
                                    </p>
                                    <p className="text-xs text-muted-foreground capitalize">
                                       {transaction.status}
                                    </p>
                                 </div>
                              </div>
                           );
                        })
                     )}
                  </div>
               </div>
            </motion.div>
         </div>
      </div>
   );
}
