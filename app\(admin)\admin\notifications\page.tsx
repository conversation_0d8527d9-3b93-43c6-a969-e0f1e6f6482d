/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
   useAuth,
   useCreateNotificationMessage,
   useNotificationMessages,
} from "@/hooks";
import { useUsers } from "@/hooks/use-admin";
import {
   useCreateGlobalNotification,
   useCreateUserNotification,
} from "@/hooks/use-user-notifications";
import { motion } from "framer-motion";
import {
   Alert<PERSON>riangle,
   Bell,
   CheckCircle,
   Edit,
   Eye,
   Filter,
   Info,
   MessageSquare,
   Plus,
   Trash2,
   User,
   Users,
   XCircle,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function NotificationManagement() {
   const { user } = useAuth();
   const [searchTerm, setSearchTerm] = useState("");
   const [categoryFilter, setCategoryFilter] = useState<
      "all" | "transaction" | "security" | "account" | "system" | "general"
   >("all");
   const [typeFilter, setTypeFilter] = useState<
      "all" | "info" | "success" | "warning" | "error"
   >("all");
   const [showCreateForm, setShowCreateForm] = useState(false);
   const [selectedMessage, setSelectedMessage] = useState<any>(null);
   const [activeTab, setActiveTab] = useState<"templates" | "send">(
      "templates"
   );
   const [sendType, setSendType] = useState<"individual" | "global">(
      "individual"
   );

   // Form state
   const [formData, setFormData] = useState({
      key: "",
      title: "",
      message: "",
      type: "info" as "info" | "success" | "warning" | "error",
      category: "general" as
         | "transaction"
         | "security"
         | "account"
         | "system"
         | "general",
      variables: [] as string[],
      isActive: true,
   });

   // Send notification form state
   const [sendFormData, setSendFormData] = useState({
      userId: "",
      title: "",
      message: "",
      type: "info" as "info" | "success" | "warning" | "error",
      category: "general" as
         | "transaction"
         | "security"
         | "account"
         | "system"
         | "general",
      expiresAt: "",
   });

   const {
      data: messages,
      isLoading,
      error,
      refetch,
   } = useNotificationMessages();
   const createMessageMutation = useCreateNotificationMessage();

   // Get users for individual notifications
   const { data: usersData } = useUsers({
      page: 1,
      limit: 100,
   });

   const users = usersData?.users || [];

   // Notification sending hooks
   const createUserNotificationMutation = useCreateUserNotification();
   const createGlobalNotificationMutation = useCreateGlobalNotification();

   const handleCreateMessage = async () => {
      if (!user || !formData.key || !formData.title || !formData.message) {
         toast.error("Please fill in all required fields");
         return;
      }

      try {
         await createMessageMutation.mutateAsync({
            ...formData,
            createdBy: user.id,
         });

         toast.success("Notification message created successfully");
         setShowCreateForm(false);
         setFormData({
            key: "",
            title: "",
            message: "",
            type: "info",
            category: "general",
            variables: [],
            isActive: true,
         });
      } catch (error) {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to create notification message"
         );
      }
   };

   const handlePreviewMessage = (message: any) => {
      setSelectedMessage(message);
   };

   const handleSendNotification = async () => {
      if (!user?.id) {
         toast.error("Admin user not found");
         return;
      }

      if (!sendFormData.title || !sendFormData.message) {
         toast.error("Please fill in title and message");
         return;
      }

      try {
         if (sendType === "global") {
            await createGlobalNotificationMutation.mutateAsync({
               title: sendFormData.title,
               message: sendFormData.message,
               type: sendFormData.type,
               category: sendFormData.category,
               createdBy: user.id,
               expiresAt: sendFormData.expiresAt
                  ? new Date(sendFormData.expiresAt)
                  : undefined,
            });
            toast.success("Global notification sent successfully");
         } else {
            if (!sendFormData.userId) {
               toast.error("Please select a user");
               return;
            }
            await createUserNotificationMutation.mutateAsync({
               userId: sendFormData.userId,
               title: sendFormData.title,
               message: sendFormData.message,
               type: sendFormData.type,
               category: sendFormData.category,
               expiresAt: sendFormData.expiresAt
                  ? new Date(sendFormData.expiresAt)
                  : undefined,
            });
            toast.success("Notification sent successfully");
         }

         // Reset form
         setSendFormData({
            userId: "",
            title: "",
            message: "",
            type: "info",
            category: "general",
            expiresAt: "",
         });
      } catch (error) {
         console.error("Notification send error:", error);
         toast.error("Failed to send notification");
      }
   };

   const getTypeIcon = (type: string) => {
      switch (type) {
         case "info":
            return <Info className="h-4 w-4 text-blue-600" />;
         case "success":
            return <CheckCircle className="h-4 w-4 text-green-600" />;
         case "warning":
            return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
         case "error":
            return <XCircle className="h-4 w-4 text-red-600" />;
         default:
            return <Bell className="h-4 w-4 text-gray-600" />;
      }
   };

   const getTypeBadge = (type: string) => {
      const variants = {
         info: {
            variant: "default" as const,
            className: "bg-blue-100 text-blue-800 border-blue-200",
         },
         success: {
            variant: "default" as const,
            className: "bg-green-100 text-green-800 border-green-200",
         },
         warning: {
            variant: "secondary" as const,
            className: "bg-yellow-100 text-yellow-800 border-yellow-200",
         },
         error: {
            variant: "destructive" as const,
            className: "bg-red-100 text-red-800 border-red-200",
         },
      };

      const config = variants[type as keyof typeof variants] || variants.info;

      return (
         <Badge variant={config.variant} className={config.className}>
            {getTypeIcon(type)}
            <span className="ml-1 capitalize">{type}</span>
         </Badge>
      );
   };

   const getCategoryBadge = (category: string) => {
      return (
         <Badge variant="outline" className="capitalize">
            {category}
         </Badge>
      );
   };

   const filteredMessages = (messages || []).filter((message) => {
      const matchesSearch =
         message.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
         message.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
         message.message.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory =
         categoryFilter === "all" || message.category === categoryFilter;
      const matchesType = typeFilter === "all" || message.type === typeFilter;

      return matchesSearch && matchesCategory && matchesType;
   });

   const stats = {
      total: messages?.length || 0,
      active: messages?.filter((m) => m.isActive).length || 0,
      inactive: messages?.filter((m) => !m.isActive).length || 0,
      byType: {
         info: messages?.filter((m) => m.type === "info").length || 0,
         success: messages?.filter((m) => m.type === "success").length || 0,
         warning: messages?.filter((m) => m.type === "warning").length || 0,
         error: messages?.filter((m) => m.type === "error").length || 0,
      },
   };

   return (
      <div className="container mx-auto px-4 py-8">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
         >
            {/* Header */}
            <div className="mb-8">
               <div className="flex items-center justify-between">
                  <div>
                     <h1 className="text-3xl font-bold text-foreground mb-2">
                        Notification Management
                     </h1>
                     <p className="text-muted-foreground">
                        Manage notification templates and send notifications to
                        users.
                     </p>
                  </div>
                  <div className="flex items-center gap-2">
                     <Button
                        variant={
                           activeTab === "templates" ? "default" : "outline"
                        }
                        onClick={() => setActiveTab("templates")}
                     >
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Templates
                     </Button>
                     <Button
                        variant={activeTab === "send" ? "default" : "outline"}
                        onClick={() => setActiveTab("send")}
                     >
                        <Bell className="h-4 w-4 mr-2" />
                        Send Notifications
                     </Button>
                  </div>
               </div>
            </div>

            {/* Templates Tab */}
            {activeTab === "templates" && (
               <>
                  {/* Action Buttons */}
                  <div className="flex justify-end mb-6">
                     <Button onClick={() => setShowCreateForm(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Template
                     </Button>
                  </div>

                  {/* Stats Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-8">
                     <Card>
                        <CardContent className="p-4">
                           <div className="flex items-center gap-2">
                              <MessageSquare className="h-5 w-5 text-muted-foreground" />
                              <div>
                                 <p className="text-sm text-muted-foreground">
                                    Total
                                 </p>
                                 <p className="text-2xl font-bold">
                                    {stats.total}
                                 </p>
                              </div>
                           </div>
                        </CardContent>
                     </Card>
                     <Card>
                        <CardContent className="p-4">
                           <div className="flex items-center gap-2">
                              <CheckCircle className="h-5 w-5 text-green-600" />
                              <div>
                                 <p className="text-sm text-muted-foreground">
                                    Active
                                 </p>
                                 <p className="text-2xl font-bold text-green-600">
                                    {stats.active}
                                 </p>
                              </div>
                           </div>
                        </CardContent>
                     </Card>
                     <Card>
                        <CardContent className="p-4">
                           <div className="flex items-center gap-2">
                              <Info className="h-5 w-5 text-blue-600" />
                              <div>
                                 <p className="text-sm text-muted-foreground">
                                    Info
                                 </p>
                                 <p className="text-2xl font-bold text-blue-600">
                                    {stats.byType.info}
                                 </p>
                              </div>
                           </div>
                        </CardContent>
                     </Card>
                     <Card>
                        <CardContent className="p-4">
                           <div className="flex items-center gap-2">
                              <CheckCircle className="h-5 w-5 text-green-600" />
                              <div>
                                 <p className="text-sm text-muted-foreground">
                                    Success
                                 </p>
                                 <p className="text-2xl font-bold text-green-600">
                                    {stats.byType.success}
                                 </p>
                              </div>
                           </div>
                        </CardContent>
                     </Card>
                     <Card>
                        <CardContent className="p-4">
                           <div className="flex items-center gap-2">
                              <AlertTriangle className="h-5 w-5 text-yellow-600" />
                              <div>
                                 <p className="text-sm text-muted-foreground">
                                    Warning
                                 </p>
                                 <p className="text-2xl font-bold text-yellow-600">
                                    {stats.byType.warning}
                                 </p>
                              </div>
                           </div>
                        </CardContent>
                     </Card>
                     <Card>
                        <CardContent className="p-4">
                           <div className="flex items-center gap-2">
                              <XCircle className="h-5 w-5 text-red-600" />
                              <div>
                                 <p className="text-sm text-muted-foreground">
                                    Error
                                 </p>
                                 <p className="text-2xl font-bold text-red-600">
                                    {stats.byType.error}
                                 </p>
                              </div>
                           </div>
                        </CardContent>
                     </Card>
                  </div>

                  {/* Create Form */}
                  {showCreateForm && (
                     <Card className="mb-6">
                        <CardHeader>
                           <CardTitle className="flex items-center gap-2">
                              <Plus className="h-5 w-5" />
                              Create New Notification Message
                           </CardTitle>
                        </CardHeader>
                        <CardContent>
                           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                 <Label htmlFor="key">Message Key *</Label>
                                 <Input
                                    id="key"
                                    placeholder="UNIQUE_MESSAGE_KEY"
                                    value={formData.key}
                                    onChange={(e) =>
                                       setFormData((prev) => ({
                                          ...prev,
                                          key: e.target.value.toUpperCase(),
                                       }))
                                    }
                                    className="mt-1"
                                 />
                              </div>
                              <div>
                                 <Label htmlFor="title">Title *</Label>
                                 <Input
                                    id="title"
                                    placeholder="Message title"
                                    value={formData.title}
                                    onChange={(e) =>
                                       setFormData((prev) => ({
                                          ...prev,
                                          title: e.target.value,
                                       }))
                                    }
                                    className="mt-1"
                                 />
                              </div>
                              <div>
                                 <Label htmlFor="type">Type</Label>
                                 <Select
                                    value={formData.type}
                                    onValueChange={(value: any) =>
                                       setFormData((prev) => ({
                                          ...prev,
                                          type: value,
                                       }))
                                    }
                                 >
                                    <SelectTrigger className="mt-1">
                                       <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                       <SelectItem value="info">
                                          Info
                                       </SelectItem>
                                       <SelectItem value="success">
                                          Success
                                       </SelectItem>
                                       <SelectItem value="warning">
                                          Warning
                                       </SelectItem>
                                       <SelectItem value="error">
                                          Error
                                       </SelectItem>
                                    </SelectContent>
                                 </Select>
                              </div>
                              <div>
                                 <Label htmlFor="category">Category</Label>
                                 <Select
                                    value={formData.category}
                                    onValueChange={(value: any) =>
                                       setFormData((prev) => ({
                                          ...prev,
                                          category: value,
                                       }))
                                    }
                                 >
                                    <SelectTrigger className="mt-1">
                                       <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                       <SelectItem value="transaction">
                                          Transaction
                                       </SelectItem>
                                       <SelectItem value="security">
                                          Security
                                       </SelectItem>
                                       <SelectItem value="account">
                                          Account
                                       </SelectItem>
                                       <SelectItem value="system">
                                          System
                                       </SelectItem>
                                       <SelectItem value="general">
                                          General
                                       </SelectItem>
                                    </SelectContent>
                                 </Select>
                              </div>
                              <div className="md:col-span-2">
                                 <Label htmlFor="message">Message *</Label>
                                 <Textarea
                                    id="message"
                                    placeholder="Your message content here. Use {variableName} for dynamic content."
                                    value={formData.message}
                                    onChange={(e) =>
                                       setFormData((prev) => ({
                                          ...prev,
                                          message: e.target.value,
                                       }))
                                    }
                                    className="mt-1"
                                    rows={3}
                                 />
                              </div>
                              <div className="md:col-span-2">
                                 <div className="flex items-center justify-between">
                                    <div className="flex gap-4">
                                       <Button
                                          onClick={handleCreateMessage}
                                          disabled={
                                             createMessageMutation.isPending ||
                                             !formData.key ||
                                             !formData.title ||
                                             !formData.message
                                          }
                                       >
                                          Create Message
                                       </Button>
                                       <Button
                                          variant="outline"
                                          onClick={() =>
                                             setShowCreateForm(false)
                                          }
                                       >
                                          Cancel
                                       </Button>
                                    </div>
                                    <div className="flex items-center gap-2">
                                       <Switch
                                          checked={formData.isActive}
                                          onCheckedChange={(checked) =>
                                             setFormData((prev) => ({
                                                ...prev,
                                                isActive: checked,
                                             }))
                                          }
                                       />
                                       <Label>Active</Label>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </CardContent>
                     </Card>
                  )}

                  {/* Filters */}
                  <Card className="mb-6">
                     <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                           <Filter className="h-5 w-5" />
                           Search & Filter
                        </CardTitle>
                     </CardHeader>
                     <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                           <div>
                              <Label htmlFor="search">Search Messages</Label>
                              <Input
                                 id="search"
                                 placeholder="Search by title, key, or content..."
                                 value={searchTerm}
                                 onChange={(e) => setSearchTerm(e.target.value)}
                                 className="mt-1"
                              />
                           </div>
                           <div>
                              <Label htmlFor="category-filter">Category</Label>
                              <Select
                                 value={categoryFilter}
                                 onValueChange={(value: any) =>
                                    setCategoryFilter(value)
                                 }
                              >
                                 <SelectTrigger className="mt-1">
                                    <SelectValue placeholder="Filter by category" />
                                 </SelectTrigger>
                                 <SelectContent>
                                    <SelectItem value="all">
                                       All Categories
                                    </SelectItem>
                                    <SelectItem value="transaction">
                                       Transaction
                                    </SelectItem>
                                    <SelectItem value="security">
                                       Security
                                    </SelectItem>
                                    <SelectItem value="account">
                                       Account
                                    </SelectItem>
                                    <SelectItem value="system">
                                       System
                                    </SelectItem>
                                    <SelectItem value="general">
                                       General
                                    </SelectItem>
                                 </SelectContent>
                              </Select>
                           </div>
                           <div>
                              <Label htmlFor="type-filter">Type</Label>
                              <Select
                                 value={typeFilter}
                                 onValueChange={(value: any) =>
                                    setTypeFilter(value)
                                 }
                              >
                                 <SelectTrigger className="mt-1">
                                    <SelectValue placeholder="Filter by type" />
                                 </SelectTrigger>
                                 <SelectContent>
                                    <SelectItem value="all">
                                       All Types
                                    </SelectItem>
                                    <SelectItem value="info">Info</SelectItem>
                                    <SelectItem value="success">
                                       Success
                                    </SelectItem>
                                    <SelectItem value="warning">
                                       Warning
                                    </SelectItem>
                                    <SelectItem value="error">Error</SelectItem>
                                 </SelectContent>
                              </Select>
                           </div>
                        </div>
                     </CardContent>
                  </Card>

                  {/* Messages List */}
                  <Card>
                     <CardHeader>
                        <CardTitle>
                           Notification Messages ({filteredMessages.length})
                        </CardTitle>
                     </CardHeader>
                     <CardContent>
                        {isLoading ? (
                           <div className="space-y-4">
                              {Array.from({ length: 5 }).map((_, index) => (
                                 <div
                                    key={index}
                                    className="flex items-center gap-4 p-4 border rounded-lg"
                                 >
                                    <Skeleton className="h-10 w-10 rounded-full" />
                                    <div className="flex-1 space-y-2">
                                       <Skeleton className="h-4 w-48" />
                                       <Skeleton className="h-3 w-32" />
                                    </div>
                                    <Skeleton className="h-6 w-20" />
                                    <Skeleton className="h-8 w-24" />
                                 </div>
                              ))}
                           </div>
                        ) : error ? (
                           <div className="text-center py-8">
                              <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                              <p className="text-red-600 mb-4">
                                 Failed to load notification messages
                              </p>
                              <Button
                                 onClick={() => refetch()}
                                 variant="outline"
                              >
                                 Try Again
                              </Button>
                           </div>
                        ) : filteredMessages.length === 0 ? (
                           <div className="text-center py-8">
                              <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                              <p className="text-muted-foreground">
                                 No notification messages found
                              </p>
                           </div>
                        ) : (
                           <div className="space-y-4">
                              {filteredMessages.map((message) => (
                                 <div
                                    key={message.id}
                                    className="flex items-center gap-4 p-4 border rounded-lg hover:bg-muted/30 transition-colors"
                                 >
                                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                                       {getTypeIcon(message.type)}
                                    </div>
                                    <div className="flex-1">
                                       <div className="flex items-center gap-2 mb-1">
                                          <p className="font-medium">
                                             {message.title}
                                          </p>
                                          {getTypeBadge(message.type)}
                                          {getCategoryBadge(message.category)}
                                          {!message.isActive && (
                                             <Badge
                                                variant="outline"
                                                className="text-gray-500"
                                             >
                                                Inactive
                                             </Badge>
                                          )}
                                       </div>
                                       <p className="text-sm text-muted-foreground mb-1">
                                          Key: {message.key}
                                       </p>
                                       <p className="text-sm text-muted-foreground line-clamp-2">
                                          {message.message}
                                       </p>
                                       {message.variables.length > 0 && (
                                          <p className="text-xs text-muted-foreground mt-1">
                                             Variables:{" "}
                                             {message.variables.join(", ")}
                                          </p>
                                       )}
                                    </div>
                                    <div className="flex gap-2">
                                       <Button
                                          size="sm"
                                          variant="outline"
                                          onClick={() =>
                                             handlePreviewMessage(message)
                                          }
                                       >
                                          <Eye className="w-4 h-4" />
                                       </Button>
                                       <Button size="sm" variant="outline">
                                          <Edit className="w-4 h-4" />
                                       </Button>
                                       <Button
                                          size="sm"
                                          variant="outline"
                                          className="text-red-600 border-red-200 hover:bg-red-50"
                                       >
                                          <Trash2 className="w-4 h-4" />
                                       </Button>
                                    </div>
                                 </div>
                              ))}
                           </div>
                        )}
                     </CardContent>
                  </Card>

                  {/* Preview Modal */}
                  {selectedMessage && (
                     <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                        <Card className="w-full max-w-md mx-4">
                           <CardHeader>
                              <CardTitle className="flex items-center gap-2">
                                 {getTypeIcon(selectedMessage.type)}
                                 Message Preview
                              </CardTitle>
                           </CardHeader>
                           <CardContent>
                              <div className="space-y-4">
                                 <div>
                                    <p className="font-semibold">
                                       {selectedMessage.title}
                                    </p>
                                    <p className="text-sm text-muted-foreground">
                                       {selectedMessage.message}
                                    </p>
                                 </div>
                                 <div className="flex items-center gap-2">
                                    {getTypeBadge(selectedMessage.type)}
                                    {getCategoryBadge(selectedMessage.category)}
                                 </div>
                                 <Button
                                    onClick={() => setSelectedMessage(null)}
                                    className="w-full"
                                 >
                                    Close
                                 </Button>
                              </div>
                           </CardContent>
                        </Card>
                     </div>
                  )}
               </>
            )}

            {/* Send Notifications Tab */}
            {activeTab === "send" && (
               <>
                  {/* Send Type Selection */}
                  <div className="flex justify-center mb-6">
                     <div className="flex items-center gap-2 p-1 bg-muted rounded-lg">
                        <Button
                           variant={
                              sendType === "individual" ? "default" : "ghost"
                           }
                           size="sm"
                           onClick={() => setSendType("individual")}
                        >
                           <User className="h-4 w-4 mr-2" />
                           Individual
                        </Button>
                        <Button
                           variant={sendType === "global" ? "default" : "ghost"}
                           size="sm"
                           onClick={() => setSendType("global")}
                        >
                           <Users className="h-4 w-4 mr-2" />
                           Global
                        </Button>
                     </div>
                  </div>

                  {/* Send Notification Form */}
                  <Card>
                     <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                           <Bell className="h-5 w-5" />
                           Send{" "}
                           {sendType === "global"
                              ? "Global"
                              : "Individual"}{" "}
                           Notification
                        </CardTitle>
                     </CardHeader>
                     <CardContent>
                        <div className="space-y-4">
                           {sendType === "individual" && (
                              <div className="space-y-2">
                                 <Label htmlFor="userId">Select User</Label>
                                 <Select
                                    value={sendFormData.userId}
                                    onValueChange={(value) =>
                                       setSendFormData((prev) => ({
                                          ...prev,
                                          userId: value,
                                       }))
                                    }
                                 >
                                    <SelectTrigger>
                                       <SelectValue placeholder="Choose a user" />
                                    </SelectTrigger>
                                    <SelectContent>
                                       {users.map((user) => (
                                          <SelectItem
                                             key={user.id}
                                             value={user.id}
                                          >
                                             {user.firstName} {user.lastName} (
                                             {user.email})
                                          </SelectItem>
                                       ))}
                                    </SelectContent>
                                 </Select>
                              </div>
                           )}

                           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                 <Label htmlFor="title">Title</Label>
                                 <Input
                                    id="title"
                                    placeholder="Notification title"
                                    value={sendFormData.title}
                                    onChange={(e) =>
                                       setSendFormData((prev) => ({
                                          ...prev,
                                          title: e.target.value,
                                       }))
                                    }
                                 />
                              </div>
                              <div className="space-y-2">
                                 <Label htmlFor="type">Type</Label>
                                 <Select
                                    value={sendFormData.type}
                                    onValueChange={(
                                       value:
                                          | "info"
                                          | "success"
                                          | "warning"
                                          | "error"
                                    ) =>
                                       setSendFormData((prev) => ({
                                          ...prev,
                                          type: value,
                                       }))
                                    }
                                 >
                                    <SelectTrigger>
                                       <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                       <SelectItem value="info">
                                          Info
                                       </SelectItem>
                                       <SelectItem value="success">
                                          Success
                                       </SelectItem>
                                       <SelectItem value="warning">
                                          Warning
                                       </SelectItem>
                                       <SelectItem value="error">
                                          Error
                                       </SelectItem>
                                    </SelectContent>
                                 </Select>
                              </div>
                           </div>

                           <div className="space-y-2">
                              <Label htmlFor="message">Message</Label>
                              <Textarea
                                 id="message"
                                 placeholder="Notification message"
                                 rows={4}
                                 value={sendFormData.message}
                                 onChange={(e) =>
                                    setSendFormData((prev) => ({
                                       ...prev,
                                       message: e.target.value,
                                    }))
                                 }
                              />
                           </div>

                           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                 <Label htmlFor="category">Category</Label>
                                 <Select
                                    value={sendFormData.category}
                                    onValueChange={(
                                       value: typeof sendFormData.category
                                    ) =>
                                       setSendFormData((prev) => ({
                                          ...prev,
                                          category: value,
                                       }))
                                    }
                                 >
                                    <SelectTrigger>
                                       <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                       <SelectItem value="general">
                                          General
                                       </SelectItem>
                                       <SelectItem value="transaction">
                                          Transaction
                                       </SelectItem>
                                       <SelectItem value="security">
                                          Security
                                       </SelectItem>
                                       <SelectItem value="account">
                                          Account
                                       </SelectItem>
                                       <SelectItem value="system">
                                          System
                                       </SelectItem>
                                    </SelectContent>
                                 </Select>
                              </div>
                              <div className="space-y-2">
                                 <Label htmlFor="expiresAt">
                                    Expires At (Optional)
                                 </Label>
                                 <Input
                                    id="expiresAt"
                                    type="datetime-local"
                                    value={sendFormData.expiresAt}
                                    onChange={(e) =>
                                       setSendFormData((prev) => ({
                                          ...prev,
                                          expiresAt: e.target.value,
                                       }))
                                    }
                                 />
                              </div>
                           </div>

                           <div className="flex justify-end gap-2 pt-4">
                              <Button
                                 onClick={handleSendNotification}
                                 disabled={
                                    createUserNotificationMutation.isPending ||
                                    createGlobalNotificationMutation.isPending
                                 }
                              >
                                 <Bell className="h-4 w-4 mr-2" />
                                 Send Notification
                              </Button>
                           </div>
                        </div>
                     </CardContent>
                  </Card>
               </>
            )}
         </motion.div>
      </div>
   );
}
