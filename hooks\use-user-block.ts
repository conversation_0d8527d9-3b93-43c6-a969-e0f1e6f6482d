"use client";

import {
   blockUserAction,
   getAllBlockedUsersAction,
   getUserBlockStatusAction,
   unblockUserAction,
   updateUserBlockAction,
} from "@/lib/actions";
import {
   CreateUserBlockData,
   UpdateUserBlockData,
   UserBlockDetails,
} from "@/lib/models";
import {
   useMutation,
   useQuery,
   useQueryClient,
   UseQueryResult,
} from "@tanstack/react-query";

/**
 * Hook to get user block status
 */
export function useUserBlockStatus(
   userId: string | undefined
): UseQueryResult<UserBlockDetails, Error> {
   return useQuery({
      queryKey: ["user-block-status", userId],
      queryFn: async () => {
         if (!userId) throw new Error("User ID is required");
         const result = await getUserBlockStatusAction(userId);
         if (!result.success) {
            throw new Error(
               result.message || "Failed to get user block status"
            );
         }
         return (
            result.block ?? {
               userId,
               id: "",
               blockTitle: "",
               blockMessage: "",
               blockReason: "",
               blockedBy: "",
               blockedAt: new Date(),
               isBlocked: false, // Default value if result.block is undefined
            }
         );
      },
      // queryFn: async () => {
      //    if (!userId) throw new Error("User ID is required");
      //    const result = await getUserBlockStatusAction(userId);
      //    if (!result.success) {
      //       throw new Error(
      //          result.message || "Failed to get user block status"
      //       );
      //    }
      //    return result.block || {}; // Return an empty object if result.block is undefined
      // },
      // queryFn: async () => {
      //    if (!userId) throw new Error("User ID is required");
      //    const result = await getUserBlockStatusAction(userId);
      //    if (!result.success) {
      //       throw new Error(
      //          result.message || "Failed to get user block status"
      //       );
      //    }
      //    return result.block ?? { status: "unknown" }; // Return a default object with a status of 'unknown'
      // },
      enabled: !!userId,
      staleTime: 30 * 1000, // 30 seconds
   });
}

/**
 * Hook to get all blocked users
 */
export function useAllBlockedUsers(options: {
   page?: number;
   limit?: number;
   search?: string;
}) {
   return useQuery({
      queryKey: ["blocked-users", options],
      queryFn: async () => {
         const result = await getAllBlockedUsersAction(options);
         if (!result.success) {
            throw new Error(result.message || "Failed to get blocked users");
         }
         return result;
      },
      staleTime: 30 * 1000, // 30 seconds
   });
}

/**
 * Hook to block user
 */
export function useBlockUser() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async (data: CreateUserBlockData) => {
         const result = await blockUserAction(data);
         if (!result.success) {
            throw new Error(result.message || "Failed to block user");
         }
         return result.block!;
      },
      onSuccess: (data, variables) => {
         // Invalidate and refetch relevant queries
         queryClient.invalidateQueries({
            queryKey: ["user-block-status", variables.userId],
         });
         queryClient.invalidateQueries({ queryKey: ["blocked-users"] });
         queryClient.invalidateQueries({ queryKey: ["admin", "users"] });
         queryClient.invalidateQueries({
            queryKey: ["admin", "user-details", variables.userId],
         });
      },
   });
}

/**
 * Hook to unblock user
 */
export function useUnblockUser() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async ({
         userId,
         unblockedBy,
      }: {
         userId: string;
         unblockedBy: string;
      }) => {
         const result = await unblockUserAction(userId, unblockedBy);
         if (!result.success) {
            throw new Error(result.message || "Failed to unblock user");
         }
         return result;
      },
      onSuccess: (data, variables) => {
         // Invalidate and refetch relevant queries
         queryClient.invalidateQueries({
            queryKey: ["user-block-status", variables.userId],
         });
         queryClient.invalidateQueries({ queryKey: ["blocked-users"] });
         queryClient.invalidateQueries({ queryKey: ["admin", "users"] });
         queryClient.invalidateQueries({
            queryKey: ["admin", "user-details", variables.userId],
         });
      },
   });
}

/**
 * Hook to update user block
 */
export function useUpdateUserBlock() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async ({
         userId,
         data,
      }: {
         userId: string;
         data: UpdateUserBlockData;
      }) => {
         const result = await updateUserBlockAction(userId, data);
         if (!result.success) {
            throw new Error(result.message || "Failed to update user block");
         }
         return result.block!;
      },
      onSuccess: (data, variables) => {
         // Invalidate and refetch relevant queries
         queryClient.invalidateQueries({
            queryKey: ["user-block-status", variables.userId],
         });
         queryClient.invalidateQueries({ queryKey: ["blocked-users"] });
         queryClient.invalidateQueries({ queryKey: ["admin", "users"] });
         queryClient.invalidateQueries({
            queryKey: ["admin", "user-details", variables.userId],
         });
      },
   });
}
