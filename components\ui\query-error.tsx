"use client";

import { But<PERSON> } from "./button";
import { AlertTriangle, RefreshCw } from "lucide-react";

interface QueryErrorProps {
  error: Error | null;
  onRetry?: () => void;
  title?: string;
  description?: string;
  className?: string;
}

export function QueryError({ 
  error, 
  onRetry, 
  title = "Failed to load data",
  description,
  className = ""
}: QueryErrorProps) {
  const errorMessage = error?.message || description || "An unexpected error occurred. Please try again.";

  return (
    <div className={`flex flex-col items-center justify-center p-8 text-center ${className}`}>
      <div className="mb-4 p-3 rounded-full bg-destructive/10">
        <AlertTriangle className="h-6 w-6 text-destructive" />
      </div>
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground mb-4 max-w-md text-sm">
        {errorMessage}
      </p>
      {onRetry && (
        <Button onClick={onRetry} variant="outline" size="sm" className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          Try again
        </Button>
      )}
    </div>
  );
}

interface InlineQueryErrorProps {
  error: Error | null;
  onRetry?: () => void;
  message?: string;
}

export function InlineQueryError({ error, onRetry, message }: InlineQueryErrorProps) {
  const errorMessage = error?.message || message || "Failed to load";

  return (
    <div className="flex items-center justify-between p-3 rounded-lg border border-destructive/20 bg-destructive/5">
      <div className="flex items-center gap-2 text-sm text-destructive">
        <AlertTriangle className="h-4 w-4" />
        <span>{errorMessage}</span>
      </div>
      {onRetry && (
        <Button onClick={onRetry} variant="ghost" size="sm" className="h-6 px-2 text-destructive hover:text-destructive">
          <RefreshCw className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
}
