"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { TransactionStatusBadge } from "@/components/ui/transaction-status-badge";
import { useAuth } from "@/hooks";
import {
   useChangeTransactionStatus,
   usePendingTransactions,
} from "@/hooks/use-admin";
import { motion } from "framer-motion";
import {
   CheckCircle,
   Clock,
   DollarSign,
   Filter,
   Search,
   XCircle,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function PendingTransactions() {
   const { user } = useAuth();
   const [searchTerm, setSearchTerm] = useState("");
   const [typeFilter, setTypeFilter] = useState<"all" | "credit" | "debit">(
      "all"
   );
   const [currentPage, setCurrentPage] = useState(1);
   const pageSize = 20;

   // Get pending transactions
   const {
      data: transactionsData,
      isLoading,
      error,
      refetch,
   } = usePendingTransactions({
      type: typeFilter === "all" ? undefined : typeFilter,
      page: currentPage,
      limit: pageSize,
   });

   const changeStatusMutation = useChangeTransactionStatus();

   const transactions = transactionsData?.transactions || [];
   const totalTransactions = transactionsData?.total || 0;
   const totalPages = Math.ceil(totalTransactions / pageSize);

   const handleApprove = async (transactionId: string) => {
      if (!user?.id) return;

      try {
         await changeStatusMutation.mutateAsync({
            transactionId,
            status: "completed",
            adminId: user.id,
            reason: "Approved by admin",
         });
         toast.success("Transaction approved successfully");
         refetch();
      } catch (error) {
         console.error("Failed to approve transaction:", error);
         toast.error("Failed to approve transaction");
      }
   };

   const handleReject = async (transactionId: string) => {
      if (!user?.id) return;

      try {
         await changeStatusMutation.mutateAsync({
            transactionId,
            status: "failed",
            adminId: user.id,
            reason: "Rejected by admin",
         });
         toast.success("Transaction rejected successfully");
         refetch();
      } catch (error) {
         console.error("Failed to reject transaction:", error);
         toast.error("Failed to reject transaction");
      }
   };

   // Filter transactions based on search term
   const filteredTransactions = transactions.filter((transaction) => {
      const matchesSearch =
         searchTerm === "" ||
         transaction.description
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
         transaction.transactionId
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
         (transaction.metadata?.recipientName &&
            transaction.metadata.recipientName
               .toLowerCase()
               .includes(searchTerm.toLowerCase()));
      return matchesSearch;
   });

   if (isLoading) {
      return (
         <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
               <h1 className="text-3xl font-bold text-foreground mb-2">
                  Pending Transactions
               </h1>
               <p className="text-muted-foreground">
                  Loading pending transactions...
               </p>
            </div>
            <div className="space-y-4">
               {[1, 2, 3, 4, 5].map((i) => (
                  <Card key={i}>
                     <CardContent className="p-6">
                        <div className="animate-pulse">
                           <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                           <div className="h-3 bg-muted rounded w-1/2"></div>
                        </div>
                     </CardContent>
                  </Card>
               ))}
            </div>
         </div>
      );
   }

   if (error) {
      return (
         <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
               <h1 className="text-3xl font-bold text-foreground mb-2">
                  Pending Transactions
               </h1>
               <p className="text-destructive">
                  Failed to load pending transactions. Please try again.
               </p>
            </div>
         </div>
      );
   }

   return (
      <div className="container mx-auto px-4 py-8">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
         >
            {/* Header */}
            <div className="mb-8">
               <div className="flex items-center justify-between">
                  <div>
                     <h1 className="text-3xl font-bold text-foreground mb-2">
                        Pending Transactions
                     </h1>
                     <p className="text-muted-foreground">
                        Review and approve pending transactions requiring admin
                        approval.
                     </p>
                  </div>
                  <div className="flex items-center gap-2">
                     <Clock className="h-5 w-5 text-warning" />
                     <span className="text-sm font-medium">
                        {totalTransactions} pending
                     </span>
                  </div>
               </div>
            </div>

            {/* Filters */}
            <Card className="mb-6">
               <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                     <Filter className="h-5 w-5" />
                     Filters
                  </CardTitle>
               </CardHeader>
               <CardContent>
                  <div className="flex flex-col md:flex-row gap-4">
                     <div className="flex-1">
                        <div className="relative">
                           <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                           <Input
                              placeholder="Search by description, transaction ID, or recipient..."
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="pl-10"
                           />
                        </div>
                     </div>
                     <Select
                        value={typeFilter}
                        onValueChange={(value: "all" | "credit" | "debit") =>
                           setTypeFilter(value)
                        }
                     >
                        <SelectTrigger className="w-[180px]">
                           <SelectValue placeholder="Transaction Type" />
                        </SelectTrigger>
                        <SelectContent>
                           <SelectItem value="all">All Types</SelectItem>
                           <SelectItem value="credit">Credit</SelectItem>
                           <SelectItem value="debit">Debit</SelectItem>
                        </SelectContent>
                     </Select>
                  </div>
               </CardContent>
            </Card>

            {/* Transactions List */}
            <div className="space-y-4">
               {filteredTransactions.length === 0 ? (
                  <Card>
                     <CardContent className="p-8 text-center">
                        <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-semibold mb-2">
                           No Pending Transactions
                        </h3>
                        <p className="text-muted-foreground">
                           All transactions have been processed.
                        </p>
                     </CardContent>
                  </Card>
               ) : (
                  filteredTransactions.map((transaction) => (
                     <Card
                        key={transaction.id}
                        className="hover:shadow-md transition-shadow"
                     >
                        <CardContent className="p-6">
                           <div className="flex items-center justify-between">
                              <div className="flex-1">
                                 <div className="flex items-center gap-3 mb-2">
                                    <div className="flex items-center gap-2">
                                       <DollarSign className="h-4 w-4 text-muted-foreground" />
                                       <span className="font-mono text-sm">
                                          {transaction.transactionId}
                                       </span>
                                    </div>
                                    <TransactionStatusBadge
                                       status={transaction.status}
                                       showIcon={true}
                                    />
                                 </div>
                                 <h3 className="font-semibold text-lg mb-1">
                                    {transaction.description}
                                 </h3>
                                 <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                    <span>
                                       Amount: ${transaction.amount.toFixed(2)}
                                    </span>
                                    <span>Type: {transaction.type}</span>
                                    <span>
                                       Date:{" "}
                                       {new Date(
                                          transaction.date
                                       ).toLocaleDateString()}
                                    </span>
                                    {transaction.metadata?.recipientName && (
                                       <span>
                                          To:{" "}
                                          {transaction.metadata.recipientName}
                                       </span>
                                    )}
                                 </div>
                              </div>
                              <div className="flex items-center gap-2">
                                 <Button
                                    onClick={() =>
                                       handleApprove(transaction.id)
                                    }
                                    disabled={changeStatusMutation.isPending}
                                    size="sm"
                                    className="bg-success hover:bg-success/90"
                                 >
                                    <CheckCircle className="h-4 w-4 mr-1" />
                                    Approve
                                 </Button>
                                 <Button
                                    onClick={() => handleReject(transaction.id)}
                                    disabled={changeStatusMutation.isPending}
                                    size="sm"
                                    variant="destructive"
                                 >
                                    <XCircle className="h-4 w-4 mr-1" />
                                    Reject
                                 </Button>
                              </div>
                           </div>
                        </CardContent>
                     </Card>
                  ))
               )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
               <div className="flex items-center justify-center gap-2 mt-8">
                  <Button
                     variant="outline"
                     onClick={() =>
                        setCurrentPage(Math.max(1, currentPage - 1))
                     }
                     disabled={currentPage === 1}
                  >
                     Previous
                  </Button>
                  <span className="text-sm text-muted-foreground">
                     Page {currentPage} of {totalPages}
                  </span>
                  <Button
                     variant="outline"
                     onClick={() =>
                        setCurrentPage(Math.min(totalPages, currentPage + 1))
                     }
                     disabled={currentPage === totalPages}
                  >
                     Next
                  </Button>
               </div>
            )}
         </motion.div>
      </div>
   );
}
