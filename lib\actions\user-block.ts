/* eslint-disable @typescript-eslint/no-explicit-any */
"use server";

import {
   CreateUserBlockData,
   UpdateUserBlockData,
   UserBlockDetails,
} from "@/lib/models";
import {
   blockUser,
   getAllBlockedUsers,
   getUserBlockStatus,
   unblockUser,
   updateUser<PERSON>lock,
} from "@/lib/services/user-block";
import { revalidatePath } from "next/cache";

/**
 * Server Action: Get user block status
 */
export async function getUserBlockStatusAction(
   userId: string
): Promise<{ success: boolean; block?: UserBlockDetails; message?: string }> {
   try {
      const result = await getUserBlockStatus(userId);
      return result;
   } catch (error) {
      console.error("Get user block status action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Block user
 */
export async function blockUserAction(
   data: CreateUserBlockData
): Promise<{ success: boolean; block?: UserBlockDetails; message?: string }> {
   try {
      const result = await blockUser(data);

      if (result.success) {
         // Revalidate relevant paths
         revalidatePath("/admin/users");
         revalidatePath(`/admin/users/${data.userId}`);
         revalidatePath("/admin/blocked-users");
      }

      return result;
   } catch (error) {
      console.error("Block user action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Unblock user
 */
export async function unblockUserAction(
   userId: string,
   unblockedBy: string
): Promise<{ success: boolean; message?: string }> {
   try {
      const result = await unblockUser(userId, unblockedBy);

      if (result.success) {
         // Revalidate relevant paths
         revalidatePath("/admin/users");
         revalidatePath(`/admin/users/${userId}`);
         revalidatePath("/admin/blocked-users");
      }

      return result;
   } catch (error) {
      console.error("Unblock user action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Update user block
 */
export async function updateUserBlockAction(
   userId: string,
   data: UpdateUserBlockData
): Promise<{ success: boolean; block?: UserBlockDetails; message?: string }> {
   try {
      const result = await updateUserBlock(userId, data);

      if (result.success) {
         // Revalidate relevant paths
         revalidatePath("/admin/users");
         revalidatePath(`/admin/users/${userId}`);
         revalidatePath("/admin/blocked-users");
      }

      return result;
   } catch (error) {
      console.error("Update user block action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Get all blocked users
 */
export async function getAllBlockedUsersAction(options: {
   page?: number;
   limit?: number;
   search?: string;
}): Promise<{
   success: boolean;
   blocks?: any[];
   total?: number;
   message?: string;
}> {
   try {
      const result = await getAllBlockedUsers(options);
      return result;
   } catch (error) {
      console.error("Get all blocked users action error:", error);
      return { success: false, message: "Internal server error" };
   }
}
