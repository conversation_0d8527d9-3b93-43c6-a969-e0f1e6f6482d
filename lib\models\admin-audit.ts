/* eslint-disable @typescript-eslint/no-explicit-any */
import { Document, ObjectId } from "mongodb";

export interface AdminAuditLog extends Document {
   _id?: ObjectId;
   adminId: ObjectId;
   action: string;
   targetType: "user" | "transaction" | "account" | "notification" | "system";
   targetId?: ObjectId;
   details: {
      before?: any;
      after?: any;
      reason?: string;
      metadata?: Record<string, any>;
   };
   ipAddress?: string;
   userAgent?: string;
   createdAt: Date;
}

export interface AdminAuditLogDetails {
   id: string;
   adminId: string;
   adminName: string;
   action: string;
   targetType: "user" | "transaction" | "account" | "notification" | "system";
   targetId?: string;
   details: {
      before?: any;
      after?: any;
      reason?: string;
      metadata?: Record<string, any>;
   };
   ipAddress?: string;
   userAgent?: string;
   createdAt: Date;
}

export interface CreateAuditLogData {
   adminId: string;
   action: string;
   targetType: "user" | "transaction" | "account" | "notification" | "system";
   targetId?: string;
   details: {
      before?: any;
      after?: any;
      reason?: string;
      metadata?: Record<string, any>;
   };
   ipAddress?: string;
   userAgent?: string;
}

// Common admin actions
export const ADMIN_ACTIONS = {
   // User management
   USER_CREATED: "user_created",
   USER_UPDATED: "user_updated",
   USER_DELETED: "user_deleted",
   USER_ROLE_CHANGED: "user_role_changed",
   USER_VERIFICATION_CHANGED: "user_verification_changed",

   // Account management
   BALANCE_ADJUSTED: "balance_adjusted",
   ACCOUNT_STATUS_CHANGED: "account_status_changed",

   // Transaction management
   TRANSACTION_STATUS_CHANGED: "transaction_status_changed",
   TRANSACTION_CREATED: "transaction_created",
   TRANSACTION_UPDATED: "transaction_updated",
   TRANSACTION_DELETED: "transaction_deleted",

   // Notification management
   NOTIFICATION_MESSAGE_CREATED: "notification_message_created",
   NOTIFICATION_MESSAGE_UPDATED: "notification_message_updated",
   NOTIFICATION_MESSAGE_DELETED: "notification_message_deleted",

   // System actions
   ADMIN_LOGIN: "admin_login",
   ADMIN_LOGOUT: "admin_logout",
   SYSTEM_SETTINGS_CHANGED: "system_settings_changed",
} as const;

export type AdminAction = (typeof ADMIN_ACTIONS)[keyof typeof ADMIN_ACTIONS];
