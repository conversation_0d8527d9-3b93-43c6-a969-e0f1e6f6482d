import { Document, ObjectId } from "mongodb";

export interface NotificationMessage extends Document {
  _id?: ObjectId;
  key: string; // Unique identifier for the message template
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  category: "transaction" | "security" | "account" | "system" | "general";
  variables: string[]; // Array of variable names that can be used in the message
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: ObjectId; // Admin who created this message
  lastModifiedBy: ObjectId; // Admin who last modified this message
}

export interface NotificationMessageDetails {
  id: string;
  key: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  category: "transaction" | "security" | "account" | "system" | "general";
  variables: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  lastModifiedBy: string;
}

export interface CreateNotificationMessageData {
  key: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  category: "transaction" | "security" | "account" | "system" | "general";
  variables: string[];
  isActive?: boolean;
  createdBy: string;
}

export interface UpdateNotificationMessageData {
  title?: string;
  message?: string;
  type?: "info" | "success" | "warning" | "error";
  category?: "transaction" | "security" | "account" | "system" | "general";
  variables?: string[];
  isActive?: boolean;
  lastModifiedBy: string;
}

// Default notification message templates
export const DEFAULT_NOTIFICATION_MESSAGES = [
  {
    key: "TRANSFER_SUCCESS",
    title: "Transfer Successful",
    message: "Your transfer of {amount} to {recipientName} was successful.",
    type: "success" as const,
    category: "transaction" as const,
    variables: ["amount", "recipientName"],
    isActive: true,
  },
  {
    key: "TRANSFER_FAILED",
    title: "Transfer Failed",
    message: "Transaction failed due to {reason}.",
    type: "error" as const,
    category: "transaction" as const,
    variables: ["reason"],
    isActive: true,
  },
  {
    key: "INSUFFICIENT_BALANCE",
    title: "Insufficient Balance",
    message: "Transaction failed due to insufficient balance.",
    type: "error" as const,
    category: "transaction" as const,
    variables: [],
    isActive: true,
  },
  {
    key: "ACCOUNT_VERIFIED",
    title: "Account Verified",
    message: "Your account has been successfully verified.",
    type: "success" as const,
    category: "account" as const,
    variables: [],
    isActive: true,
  },
  {
    key: "SECURITY_ALERT",
    title: "Security Alert",
    message: "New device login detected from {device} on {location}.",
    type: "warning" as const,
    category: "security" as const,
    variables: ["device", "location"],
    isActive: true,
  },
  {
    key: "TRANSACTION_PENDING",
    title: "Transaction Pending",
    message: "Your transaction of {amount} is pending approval.",
    type: "info" as const,
    category: "transaction" as const,
    variables: ["amount"],
    isActive: true,
  },
  {
    key: "TRANSACTION_APPROVED",
    title: "Transaction Approved",
    message: "Your transaction of {amount} has been approved and completed.",
    type: "success" as const,
    category: "transaction" as const,
    variables: ["amount"],
    isActive: true,
  },
  {
    key: "TRANSACTION_REJECTED",
    title: "Transaction Rejected",
    message: "Your transaction of {amount} has been rejected. Reason: {reason}",
    type: "error" as const,
    category: "transaction" as const,
    variables: ["amount", "reason"],
    isActive: true,
  },
];
