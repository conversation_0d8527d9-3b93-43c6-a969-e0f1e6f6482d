"use client";

import {
   applyForCard,
   getCardDetails,
   getUserCards,
   toggleCardStatus,
   updateCardSpendingLimits,
} from "@/lib/actions";
import { CardApplication } from "@/lib/models";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

/**
 * Hook to get user's cards
 */
export function useUserCards(userId: string | undefined) {
   return useQuery({
      queryKey: ["cards", userId],
      queryFn: async () => {
         if (!userId) throw new Error("User ID is required");
         const result = await getUserCards(userId);
         if (!result.success) {
            throw new Error(result.message || "Failed to get cards");
         }
         return result.cards || [];
      },
      enabled: !!userId,
      staleTime: 2 * 60 * 1000, // 2 minutes
   });
}

/**
 * Hook to get specific card details
 */
export function useCard(cardId: string | undefined) {
   return useQuery({
      queryKey: ["card", cardId],
      queryFn: async () => {
         if (!cardId) throw new Error("Card ID is required");
         const result = await getCardDetails(cardId);
         if (!result.success) {
            throw new Error(result.message || "Failed to get card details");
         }
         return result.card!;
      },
      enabled: !!cardId,
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to apply for a new card
 */
export function useApplyForCard() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async ({
         userId,
         application,
      }: {
         userId: string;
         application: CardApplication;
      }) => {
         const result = await applyForCard(userId, application);
         if (!result.success) {
            throw new Error(result.message || "Failed to apply for card");
         }
         return result.card!;
      },
      onSuccess: (_, variables) => {
         // Invalidate cards queries
         queryClient.invalidateQueries({
            queryKey: ["cards", variables.userId],
         });
      },
   });
}

/**
 * Hook to toggle card status (block/unblock)
 */
export function useToggleCardStatus() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async ({
         cardId,
         action,
      }: {
         cardId: string;
         action: "block" | "unblock";
      }) => {
         const result = await toggleCardStatus(cardId, action);
         if (!result.success) {
            throw new Error(result.message || "Failed to update card status");
         }
         return true;
      },
      onSuccess: (_, variables) => {
         // Invalidate card queries
         queryClient.invalidateQueries({
            queryKey: ["card", variables.cardId],
         });
         queryClient.invalidateQueries({ queryKey: ["cards"] });
      },
   });
}

/**
 * Hook to update card spending limits
 */
export function useUpdateCardLimits() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async ({
         cardId,
         dailyLimit,
      }: {
         cardId: string;
         dailyLimit: number;
      }) => {
         const result = await updateCardSpendingLimits(cardId, dailyLimit);
         if (!result.success) {
            throw new Error(result.message || "Failed to update card limits");
         }
         return true;
      },
      onSuccess: (_, variables) => {
         // Invalidate card queries
         queryClient.invalidateQueries({
            queryKey: ["card", variables.cardId],
         });
         queryClient.invalidateQueries({ queryKey: ["cards"] });
      },
   });
}
