/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { TransactionStatusBadge } from "@/components/ui/transaction-status-badge";
import {
   useAdminTransactions,
   useAuth,
   useChangeTransactionStatus,
} from "@/hooks";
import { motion } from "framer-motion";
import {
   AlertTriangle,
   CheckCircle,
   Clock,
   Filter,
   RefreshCw,
   TrendingDown,
   TrendingUp,
   XCircle,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function TransactionStatusManagement() {
   const { user } = useAuth();
   const [searchTerm, setSearchTerm] = useState("");
   const [statusFilter, setStatusFilter] = useState<
      "all" | "pending" | "completed" | "failed" | "cancelled"
   >("pending");
   const [selectedTransactions, setSelectedTransactions] = useState<string[]>(
      []
   );
   const [bulkStatus, setBulkStatus] = useState<
      "completed" | "failed" | "cancelled" | ""
   >("");
   const [bulkReason, setBulkReason] = useState("");

   const changeStatusMutation = useChangeTransactionStatus();

   // Get transactions with filtering
   const {
      data: transactionsData,
      isLoading,
      error,
      refetch,
   } = useAdminTransactions({
      status: statusFilter === "all" ? undefined : statusFilter,
      limit: 50,
   });

   const transactions = transactionsData?.transactions || [];

   const handleStatusChange = async (
      transactionId: string,
      newStatus: "pending" | "completed" | "failed" | "cancelled",
      reason?: string
   ) => {
      if (!user) return;

      try {
         await changeStatusMutation.mutateAsync({
            transactionId,
            status: newStatus,
            adminId: user.id,
            reason,
         });

         toast.success(`Transaction status updated to ${newStatus}`);
      } catch (error) {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to update transaction status"
         );
      }
   };

   const handleBulkStatusChange = async () => {
      if (!bulkStatus || selectedTransactions.length === 0 || !user) {
         toast.error("Please select transactions and a status");
         return;
      }

      if (!bulkReason.trim()) {
         toast.error("Please provide a reason for the bulk action");
         return;
      }

      try {
         // Process bulk updates
         const promises = selectedTransactions.map((transactionId) =>
            changeStatusMutation.mutateAsync({
               transactionId,
               status: bulkStatus,
               adminId: user.id,
               reason: bulkReason,
            })
         );

         await Promise.all(promises);

         toast.success(
            `${selectedTransactions.length} transactions updated to ${bulkStatus} status`
         );
         setSelectedTransactions([]);
         setBulkStatus("");
         setBulkReason("");
      } catch (error) {
         console.error("Bulk status change error:", error);
         toast.error("Failed to perform bulk status change");
      }
   };

   const handleSelectTransaction = (
      transactionId: string,
      checked: boolean
   ) => {
      if (checked) {
         setSelectedTransactions((prev) => [...prev, transactionId]);
      } else {
         setSelectedTransactions((prev) =>
            prev.filter((id) => id !== transactionId)
         );
      }
   };

   const handleSelectAll = (checked: boolean) => {
      if (checked) {
         setSelectedTransactions(filteredTransactions.map((t) => t.id));
      } else {
         setSelectedTransactions([]);
      }
   };

   const getTypeIcon = (type: string) => {
      return type === "credit" ? (
         <TrendingUp className="h-4 w-4 text-green-600" />
      ) : (
         <TrendingDown className="h-4 w-4 text-red-600" />
      );
   };

   const filteredTransactions = transactions.filter((transaction) => {
      const matchesSearch =
         transaction.description
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
         transaction.transactionId
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
         transaction.userId.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesSearch;
   });

   const stats = {
      total: transactions.length,
      pending: transactions.filter((t) => t.status === "pending").length,
      completed: transactions.filter((t) => t.status === "completed").length,
      failed: transactions.filter((t) => t.status === "failed").length,
      cancelled: transactions.filter((t) => t.status === "cancelled").length,
   };

   return (
      <div className="container mx-auto px-4 py-8">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
         >
            {/* Header */}
            <div className="mb-8">
               <div className="flex items-center justify-between">
                  <div>
                     <h1 className="text-3xl font-bold text-foreground mb-2">
                        Transaction Status Management
                     </h1>
                     <p className="text-muted-foreground">
                        Change transaction statuses with bulk operations and
                        detailed tracking.
                     </p>
                  </div>
                  <Button onClick={() => refetch()} variant="outline">
                     <RefreshCw className="h-4 w-4 mr-2" />
                     Refresh
                  </Button>
               </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <div className="h-5 w-5 bg-blue-100 rounded-full flex items-center justify-center">
                           <div className="h-2 w-2 bg-blue-600 rounded-full"></div>
                        </div>
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Total
                           </p>
                           <p className="text-2xl font-bold">{stats.total}</p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <Clock className="h-5 w-5 text-yellow-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Pending
                           </p>
                           <p className="text-2xl font-bold text-yellow-600">
                              {stats.pending}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Completed
                           </p>
                           <p className="text-2xl font-bold text-green-600">
                              {stats.completed}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <XCircle className="h-5 w-5 text-red-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Failed
                           </p>
                           <p className="text-2xl font-bold text-red-600">
                              {stats.failed}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5 text-gray-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Cancelled
                           </p>
                           <p className="text-2xl font-bold text-gray-600">
                              {stats.cancelled}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
            </div>

            {/* Filters */}
            <Card className="mb-6">
               <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                     <Filter className="h-5 w-5" />
                     Search & Filter
                  </CardTitle>
               </CardHeader>
               <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                     <div>
                        <Label htmlFor="search">Search Transactions</Label>
                        <Input
                           id="search"
                           placeholder="Search by description, ID, or user..."
                           value={searchTerm}
                           onChange={(e) => setSearchTerm(e.target.value)}
                           className="mt-1"
                        />
                     </div>
                     <div>
                        <Label htmlFor="status-filter">Status Filter</Label>
                        <Select
                           value={statusFilter}
                           onValueChange={(value: any) =>
                              setStatusFilter(value)
                           }
                        >
                           <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Filter by status" />
                           </SelectTrigger>
                           <SelectContent>
                              <SelectItem value="all">All Statuses</SelectItem>
                              <SelectItem value="pending">Pending</SelectItem>
                              <SelectItem value="completed">
                                 Completed
                              </SelectItem>
                              <SelectItem value="failed">Failed</SelectItem>
                              <SelectItem value="cancelled">
                                 Cancelled
                              </SelectItem>
                           </SelectContent>
                        </Select>
                     </div>
                  </div>
               </CardContent>
            </Card>

            {/* Bulk Actions */}
            {selectedTransactions.length > 0 && (
               <Card className="mb-6">
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5" />
                        Bulk Status Change ({selectedTransactions.length}{" "}
                        selected)
                     </CardTitle>
                  </CardHeader>
                  <CardContent>
                     <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                           <Label htmlFor="bulk-status">New Status</Label>
                           <Select
                              value={bulkStatus}
                              onValueChange={(value: any) =>
                                 setBulkStatus(value)
                              }
                           >
                              <SelectTrigger className="mt-1">
                                 <SelectValue placeholder="Select new status" />
                              </SelectTrigger>
                              <SelectContent>
                                 <SelectItem value="completed">
                                    Completed
                                 </SelectItem>
                                 <SelectItem value="failed">Failed</SelectItem>
                                 <SelectItem value="cancelled">
                                    Cancelled
                                 </SelectItem>
                              </SelectContent>
                           </Select>
                        </div>
                        <div>
                           <Label htmlFor="bulk-reason">Reason</Label>
                           <Input
                              id="bulk-reason"
                              placeholder="Reason for status change..."
                              value={bulkReason}
                              onChange={(e) => setBulkReason(e.target.value)}
                              className="mt-1"
                           />
                        </div>
                        <div className="flex items-end">
                           <Button
                              onClick={handleBulkStatusChange}
                              disabled={
                                 !bulkStatus ||
                                 !bulkReason.trim() ||
                                 changeStatusMutation.isPending
                              }
                              className="w-full"
                           >
                              Update {selectedTransactions.length} transactions
                           </Button>
                        </div>
                     </div>
                  </CardContent>
               </Card>
            )}

            {/* Transactions List */}
            <Card>
               <CardHeader>
                  <div className="flex items-center justify-between">
                     <CardTitle>
                        Transactions ({filteredTransactions.length})
                     </CardTitle>
                     <div className="flex items-center gap-2">
                        <Checkbox
                           checked={
                              selectedTransactions.length ===
                                 filteredTransactions.length &&
                              filteredTransactions.length > 0
                           }
                           onCheckedChange={handleSelectAll}
                        />
                        <Label className="text-sm">Select All</Label>
                     </div>
                  </div>
               </CardHeader>
               <CardContent>
                  {isLoading ? (
                     <div className="space-y-4">
                        {Array.from({ length: 5 }).map((_, index) => (
                           <div
                              key={index}
                              className="flex items-center gap-4 p-4 border rounded-lg"
                           >
                              <Skeleton className="h-4 w-4" />
                              <Skeleton className="h-10 w-10 rounded-full" />
                              <div className="flex-1 space-y-2">
                                 <Skeleton className="h-4 w-48" />
                                 <Skeleton className="h-3 w-32" />
                              </div>
                              <Skeleton className="h-6 w-20" />
                              <Skeleton className="h-8 w-24" />
                           </div>
                        ))}
                     </div>
                  ) : error ? (
                     <div className="text-center py-8">
                        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                        <p className="text-red-600 mb-4">
                           Failed to load transactions
                        </p>
                        <Button onClick={() => refetch()} variant="outline">
                           Try Again
                        </Button>
                     </div>
                  ) : filteredTransactions.length === 0 ? (
                     <div className="text-center py-8">
                        <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground">
                           No transactions found
                        </p>
                     </div>
                  ) : (
                     <div className="space-y-4">
                        {filteredTransactions.map((transaction) => (
                           <div
                              key={transaction.id}
                              className="flex items-center gap-4 p-4 border rounded-lg hover:bg-muted/30 transition-colors"
                           >
                              <Checkbox
                                 checked={selectedTransactions.includes(
                                    transaction.id
                                 )}
                                 onCheckedChange={(checked) =>
                                    handleSelectTransaction(
                                       transaction.id,
                                       checked as boolean
                                    )
                                 }
                              />
                              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                                 {getTypeIcon(transaction.type)}
                              </div>
                              <div className="flex-1">
                                 <div className="flex items-center gap-2 mb-1">
                                    <p className="font-medium">
                                       {transaction.description}
                                    </p>
                                    <TransactionStatusBadge
                                       status={transaction.status}
                                    />
                                 </div>
                                 <p className="text-sm text-muted-foreground">
                                    ID: {transaction.transactionId} • User:{" "}
                                    {transaction.userId}
                                 </p>
                                 <p className="text-xs text-muted-foreground">
                                    {transaction.date.toLocaleString()} •
                                    Amount: $
                                    {transaction.amount.toLocaleString(
                                       "en-US",
                                       { minimumFractionDigits: 2 }
                                    )}
                                 </p>
                              </div>
                              <div className="flex gap-2">
                                 <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-green-600 border-green-200 hover:bg-green-50"
                                    onClick={() =>
                                       handleStatusChange(
                                          transaction.id,
                                          "completed",
                                          "Approved by admin"
                                       )
                                    }
                                    disabled={
                                       transaction.status === "completed" ||
                                       changeStatusMutation.isPending
                                    }
                                 >
                                    <CheckCircle className="w-4 h-4 mr-1" />
                                    Complete
                                 </Button>
                                 <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-red-600 border-red-200 hover:bg-red-50"
                                    onClick={() =>
                                       handleStatusChange(
                                          transaction.id,
                                          "failed",
                                          "Failed by admin"
                                       )
                                    }
                                    disabled={
                                       transaction.status === "failed" ||
                                       changeStatusMutation.isPending
                                    }
                                 >
                                    <XCircle className="w-4 h-4 mr-1" />
                                    Fail
                                 </Button>
                                 <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-gray-600 border-gray-200 hover:bg-gray-50"
                                    onClick={() =>
                                       handleStatusChange(
                                          transaction.id,
                                          "cancelled",
                                          "Cancelled by admin"
                                       )
                                    }
                                    disabled={
                                       transaction.status === "cancelled" ||
                                       changeStatusMutation.isPending
                                    }
                                 >
                                    <AlertTriangle className="w-4 h-4 mr-1" />
                                    Cancel
                                 </Button>
                              </div>
                           </div>
                        ))}
                     </div>
                  )}
               </CardContent>
            </Card>
         </motion.div>
      </div>
   );
}
