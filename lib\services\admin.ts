/* eslint-disable @typescript-eslint/no-explicit-any */
import {
   Account,
   AccountDetails,
   ADMIN_ACTIONS,
   AdminAuditLog,
   CreateAuditLogData,
   CreateNotificationMessageData,
   NotificationMessage,
   NotificationMessageDetails,
   Transaction,
   TransactionDetails,
   User,
   UserProfile,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { ObjectId, OptionalId } from "mongodb";
import { updateTransactionStatusWithBalance } from "./transaction";

/**
 * Admin service for managing users, accounts, transactions, and system settings
 */

// ============================================================================
// DASHBOARD STATISTICS
// ============================================================================

/**
 * Get admin dashboard statistics
 */
export async function getAdminDashboardStats(): Promise<{
   success: boolean;
   stats?: {
      totalUsers: number;
      newUsersToday: number;
      totalTransactions: number;
      pendingTransactions: number;
      totalBalance: number;
      failedTransactions: number;
      completedTransactions: number;
      unverifiedUsers: number;
      totalAccounts: number;
      activeAccounts: number;
      totalCards: number;
      pendingCardApplications: number;
   };
   message?: string;
}> {
   try {
      const usersCollection = await getCollection<User>("users");
      const accountsCollection = await getCollection<Account>("accounts");
      const transactionsCollection = await getCollection<Transaction>(
         "transactions"
      );
      const cardsCollection = await getCollection("cards");

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Run all queries in parallel for better performance
      const [
         totalUsers,
         newUsersToday,
         totalTransactions,
         pendingTransactions,
         failedTransactions,
         completedTransactions,
         unverifiedUsers,
         totalAccounts,
         activeAccounts,
         totalBalance,
         totalCards,
         pendingCardApplications,
      ] = await Promise.all([
         // User statistics
         usersCollection.countDocuments({}),
         usersCollection.countDocuments({
            createdAt: { $gte: today, $lt: tomorrow },
         }),

         // Transaction statistics
         transactionsCollection.countDocuments({}),
         transactionsCollection.countDocuments({ status: "pending" }),
         transactionsCollection.countDocuments({ status: "failed" }),
         transactionsCollection.countDocuments({ status: "completed" }),

         // User verification statistics
         usersCollection.countDocuments({ verificationStatus: "unverified" }),

         // Account statistics
         accountsCollection.countDocuments({}),
         accountsCollection.countDocuments({ accountStatus: "Active" }),

         // Total balance across all accounts
         accountsCollection
            .aggregate([
               {
                  $group: {
                     _id: null,
                     totalBalance: { $sum: "$totalBalance" },
                  },
               },
            ])
            .toArray()
            .then((result) => result[0]?.totalBalance || 0),

         // Card statistics
         cardsCollection.countDocuments({}),
         cardsCollection.countDocuments({ status: "pending" }),
      ]);

      return {
         success: true,
         stats: {
            totalUsers,
            newUsersToday,
            totalTransactions,
            pendingTransactions,
            totalBalance,
            failedTransactions,
            completedTransactions,
            unverifiedUsers,
            totalAccounts,
            activeAccounts,
            totalCards,
            pendingCardApplications,
         },
      };
   } catch (error) {
      console.error("Error getting admin dashboard stats:", error);
      return {
         success: false,
         message: "Failed to get dashboard statistics",
      };
   }
}

// ============================================================================
// CARD MANAGEMENT
// ============================================================================

/**
 * Get all cards for admin management
 */
export async function getAllCards(options: {
   page?: number;
   limit?: number;
   status?: "active" | "inactive" | "blocked" | "expired";
   cardTier?: "standard" | "gold" | "platinum" | "black";
   userId?: string;
}): Promise<{
   success: boolean;
   cards?: Array<{
      id: string;
      userId: string;
      userName: string;
      userEmail: string;
      cardNumber: string;
      cardTier: string;
      status: string;
      issuedDate: Date;
      expiryDate: Date;
      dailyLimit: number;
      monthlyLimit: number;
      currency: string;
   }>;
   total?: number;
   message?: string;
}> {
   try {
      const cardsCollection = await getCollection("cards");
      const usersCollection = await getCollection("users");

      // Build query
      const query: any = {};
      if (options.status) {
         query.status = options.status;
      }
      if (options.cardTier) {
         query.cardTier = options.cardTier;
      }
      if (options.userId) {
         query.userId = new ObjectId(options.userId);
      }

      const page = options.page || 1;
      const limit = options.limit || 20;
      const skip = (page - 1) * limit;

      const [cards, total] = await Promise.all([
         cardsCollection
            .find(query)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .toArray(),
         cardsCollection.countDocuments(query),
      ]);

      // Get user details for each card
      const cardsWithUserInfo = await Promise.all(
         cards.map(async (card) => {
            const user = await usersCollection.findOne({ _id: card.userId });
            return {
               id: card._id.toString(),
               userId: card.userId.toString(),
               userName: user
                  ? `${user.firstName} ${user.lastName}`
                  : "Unknown User",
               userEmail: user?.email || "<EMAIL>",
               cardNumber: card.cardNumber, // Already masked in database
               cardTier: card.cardTier,
               status: card.status,
               issuedDate: card.createdAt,
               expiryDate: card.expiryDate,
               dailyLimit: card.dailyLimit,
               monthlyLimit: card.monthlyLimit,
               currency: card.currency,
            };
         })
      );

      return {
         success: true,
         cards: cardsWithUserInfo,
         total,
      };
   } catch (error) {
      console.error("Error getting all cards:", error);
      return {
         success: false,
         message: "Failed to get cards",
      };
   }
}

// ============================================================================
// USER MANAGEMENT
// ============================================================================

/**
 * Get all users with pagination and filtering
 */
export async function getAllUsers(options: {
   page?: number;
   limit?: number;
   search?: string;
   role?: "user" | "admin";
   verificationStatus?: "verified" | "pending" | "unverified";
}): Promise<{
   success: boolean;
   users?: UserProfile[];
   total?: number;
   message?: string;
}> {
   try {
      const usersCollection = await getCollection<User>("users");
      const {
         page = 1,
         limit = 20,
         search,
         role,
         verificationStatus,
      } = options;
      const skip = (page - 1) * limit;

      // Build query
      const query: any = {};

      if (search) {
         query.$or = [
            { firstName: { $regex: search, $options: "i" } },
            { lastName: { $regex: search, $options: "i" } },
            { email: { $regex: search, $options: "i" } },
            { username: { $regex: search, $options: "i" } },
         ];
      }

      if (role) {
         query.role = role;
      }

      if (verificationStatus) {
         query.verificationStatus = verificationStatus;
      }

      const [users, total] = await Promise.all([
         usersCollection
            .find(query)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .toArray(),
         usersCollection.countDocuments(query),
      ]);

      const userProfiles: UserProfile[] = users.map((user) => ({
         id: user._id!.toString(),
         email: user.email,
         firstName: user.firstName,
         lastName: user.lastName,
         username: user.username,
         phoneNumber: user.phoneNumber,
         country: user.country,
         accountType: user.accountType,
         role: user.role,
         verificationStatus: user.verificationStatus,
      }));

      return { success: true, users: userProfiles, total };
   } catch (error) {
      console.error("Error getting all users:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Update user verification status
 */
export async function updateUserVerificationStatus(
   userId: string,
   verificationStatus: "verified" | "pending" | "unverified",
   adminId: string,
   reason?: string
): Promise<{ success: boolean; user?: UserProfile; message?: string }> {
   try {
      const usersCollection = await getCollection<User>("users");

      const user = await usersCollection.findOne({ _id: new ObjectId(userId) });
      if (!user) {
         return { success: false, message: "User not found" };
      }

      const oldStatus = user.verificationStatus;

      const result = await usersCollection.findOneAndUpdate(
         { _id: new ObjectId(userId) },
         {
            $set: {
               verificationStatus,
               updatedAt: new Date(),
            },
         },
         { returnDocument: "after" }
      );

      if (!result) {
         return { success: false, message: "Failed to update user" };
      }

      // Log the action
      await logAdminAction({
         adminId,
         action: ADMIN_ACTIONS.USER_VERIFICATION_CHANGED,
         targetType: "user",
         targetId: userId,
         details: {
            before: { verificationStatus: oldStatus },
            after: { verificationStatus },
            reason,
         },
      });

      const userProfile: UserProfile = {
         id: result._id!.toString(),
         email: result.email,
         firstName: result.firstName,
         lastName: result.lastName,
         username: result.username,
         phoneNumber: result.phoneNumber,
         country: result.country,
         accountType: result.accountType,
         role: result.role,
         verificationStatus: result.verificationStatus,
      };

      return { success: true, user: userProfile };
   } catch (error) {
      console.error("Error updating user verification status:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Update user role
 */
export async function updateUserRole(
   userId: string,
   role: "user" | "admin",
   adminId: string,
   reason?: string
): Promise<{ success: boolean; user?: UserProfile; message?: string }> {
   try {
      const usersCollection = await getCollection<User>("users");

      const user = await usersCollection.findOne({ _id: new ObjectId(userId) });
      if (!user) {
         return { success: false, message: "User not found" };
      }

      const oldRole = user.role;

      const result = await usersCollection.findOneAndUpdate(
         { _id: new ObjectId(userId) },
         {
            $set: {
               role,
               updatedAt: new Date(),
            },
         },
         { returnDocument: "after" }
      );

      if (!result) {
         return { success: false, message: "Failed to update user" };
      }

      // Log the action
      await logAdminAction({
         adminId,
         action: ADMIN_ACTIONS.USER_ROLE_CHANGED,
         targetType: "user",
         targetId: userId,
         details: {
            before: { role: oldRole },
            after: { role },
            reason,
         },
      });

      const userProfile: UserProfile = {
         id: result._id!.toString(),
         email: result.email,
         firstName: result.firstName,
         lastName: result.lastName,
         username: result.username,
         phoneNumber: result.phoneNumber,
         country: result.country,
         accountType: result.accountType,
         role: result.role,
         verificationStatus: result.verificationStatus,
      };

      return { success: true, user: userProfile };
   } catch (error) {
      console.error("Error updating user role:", error);
      return { success: false, message: "Internal server error" };
   }
}

// ============================================================================
// ACCOUNT MANAGEMENT
// ============================================================================

/**
 * Get user account details
 */
export async function getUserAccountDetails(
   userId: string
): Promise<{ success: boolean; account?: AccountDetails; message?: string }> {
   try {
      const accountsCollection = await getCollection<Account>("accounts");

      const account = await accountsCollection.findOne({
         userId: new ObjectId(userId),
      });

      if (!account) {
         return { success: false, message: "Account not found" };
      }

      const accountDetails: AccountDetails = {
         id: account._id!.toString(),
         userId: account.userId.toString(),
         accountNumber: account.accountNumber,
         routingNumber: account.routingNumber,
         accountType: account.accountType,
         accountStatus: account.accountStatus,
         availableBalance: account.availableBalance,
         totalBalance: account.totalBalance,
         currency: account.currency,
         dateOpened: account.dateOpened,
         branch: account.branch,
      };

      return { success: true, account: accountDetails };
   } catch (error) {
      console.error("Error getting user account details:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Get comprehensive user details for admin (user + account + cards + transactions)
 */
export async function getComprehensiveUserDetails(userId: string): Promise<{
   success: boolean;
   user?: UserProfile;
   account?: AccountDetails;
   cards?: any[];
   recentTransactions?: TransactionDetails[];
   message?: string;
}> {
   try {
      const usersCollection = await getCollection<User>("users");
      const accountsCollection = await getCollection<Account>("accounts");
      const cardsCollection = await getCollection("cards");
      const transactionsCollection = await getCollection<Transaction>(
         "transactions"
      );

      // Get user
      const user = await usersCollection.findOne({ _id: new ObjectId(userId) });
      if (!user) {
         return { success: false, message: "User not found" };
      }

      const userProfile: UserProfile = {
         id: user._id!.toString(),
         email: user.email,
         firstName: user.firstName,
         lastName: user.lastName,
         username: user.username,
         phoneNumber: user.phoneNumber,
         country: user.country,
         accountType: user.accountType,
         role: user.role,
         verificationStatus: user.verificationStatus,
      };

      // Get account
      const account = await accountsCollection.findOne({
         userId: new ObjectId(userId),
      });

      let accountDetails: AccountDetails | undefined;
      if (account) {
         accountDetails = {
            id: account._id!.toString(),
            userId: account.userId.toString(),
            accountNumber: account.accountNumber,
            routingNumber: account.routingNumber,
            accountType: account.accountType,
            accountStatus: account.accountStatus,
            availableBalance: account.availableBalance,
            totalBalance: account.totalBalance,
            currency: account.currency,
            dateOpened: account.dateOpened,
            branch: account.branch,
         };
      }

      // Get cards
      const cards = await cardsCollection
         .find({ userId: new ObjectId(userId) })
         .toArray();

      // Get recent transactions (last 10)
      const transactions = await transactionsCollection
         .find({ userId: new ObjectId(userId) })
         .sort({ createdAt: -1 })
         .limit(10)
         .toArray();

      const transactionDetails: TransactionDetails[] = transactions.map(
         (transaction) => ({
            id: transaction._id!.toString(),
            userId: transaction.userId.toString(),
            accountId: transaction.accountId.toString(),
            transactionId: transaction.transactionId,
            type: transaction.type,
            amount: transaction.amount,
            currency: transaction.currency,
            description: transaction.description,
            merchant: transaction.merchant,
            category: transaction.category,
            status: transaction.status,
            reference: transaction.reference,
            balanceAfter: transaction.balanceAfter,
            metadata: transaction.metadata
               ? {
                    ...transaction.metadata,
                    cardId: transaction.metadata.cardId?.toString(),
                 }
               : undefined,
            date: transaction.createdAt,
         })
      );

      return {
         success: true,
         user: userProfile,
         account: accountDetails,
         cards,
         recentTransactions: transactionDetails,
      };
   } catch (error) {
      console.error("Error getting comprehensive user details:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Update user profile by admin
 */
export async function updateUserProfileByAdmin(
   userId: string,
   updates: Partial<
      Pick<
         User,
         | "firstName"
         | "lastName"
         | "email"
         | "phoneNumber"
         | "country"
         | "accountType"
         | "role"
         | "verificationStatus"
      >
   >,
   adminId: string
): Promise<{ success: boolean; user?: UserProfile; message?: string }> {
   try {
      const usersCollection = await getCollection<User>("users");

      const existingUser = await usersCollection.findOne({
         _id: new ObjectId(userId),
      });
      if (!existingUser) {
         return { success: false, message: "User not found" };
      }

      // Check if email is being changed and if it's already taken
      if (updates.email && updates.email !== existingUser.email) {
         const emailExists = await usersCollection.findOne({
            email: updates.email,
            _id: { $ne: new ObjectId(userId) },
         });
         if (emailExists) {
            return { success: false, message: "Email already exists" };
         }
      }

      const updateData = {
         ...updates,
         updatedAt: new Date(),
      };

      const result = await usersCollection.findOneAndUpdate(
         { _id: new ObjectId(userId) },
         { $set: updateData },
         { returnDocument: "after" }
      );

      if (!result) {
         return { success: false, message: "Failed to update user" };
      }

      // Log the action
      await logAdminAction({
         adminId,
         action: ADMIN_ACTIONS.USER_UPDATED,
         targetType: "user",
         targetId: userId,
         details: {
            before: {
               firstName: existingUser.firstName,
               lastName: existingUser.lastName,
               email: existingUser.email,
               phoneNumber: existingUser.phoneNumber,
               country: existingUser.country,
               accountType: existingUser.accountType,
               role: existingUser.role,
               verificationStatus: existingUser.verificationStatus,
            },
            after: updates,
         },
      });

      const userProfile: UserProfile = {
         id: result._id!.toString(),
         email: result.email,
         firstName: result.firstName,
         lastName: result.lastName,
         username: result.username,
         phoneNumber: result.phoneNumber,
         country: result.country,
         accountType: result.accountType,
         role: result.role,
         verificationStatus: result.verificationStatus,
      };

      return { success: true, user: userProfile };
   } catch (error) {
      console.error("Error updating user profile by admin:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Get all user accounts with user details for admin
 */
export async function getAllUserAccounts(options: {
   page?: number;
   limit?: number;
   search?: string;
}): Promise<{
   success: boolean;
   accounts?: any[];
   total?: number;
   message?: string;
}> {
   try {
      const usersCollection = await getCollection<User>("users");
      const accountsCollection = await getCollection<Account>("accounts");

      const { page = 1, limit = 20, search } = options;
      const skip = (page - 1) * limit;

      // Build aggregation pipeline
      const pipeline: any[] = [
         {
            $lookup: {
               from: "accounts",
               localField: "_id",
               foreignField: "userId",
               as: "account",
            },
         },
         {
            $unwind: {
               path: "$account",
               preserveNullAndEmptyArrays: false,
            },
         },
      ];

      // Add search filter if provided
      if (search) {
         pipeline.push({
            $match: {
               $or: [
                  { firstName: { $regex: search, $options: "i" } },
                  { lastName: { $regex: search, $options: "i" } },
                  { email: { $regex: search, $options: "i" } },
                  {
                     "account.accountNumber": { $regex: search, $options: "i" },
                  },
               ],
            },
         });
      }

      // Add pagination
      pipeline.push({ $skip: skip }, { $limit: limit });

      const results = await usersCollection.aggregate(pipeline).toArray();

      // Get total count for pagination
      const countPipeline = [...pipeline];
      countPipeline.pop(); // Remove limit
      countPipeline.pop(); // Remove skip
      countPipeline.push({ $count: "total" });

      const countResult = await usersCollection
         .aggregate(countPipeline)
         .toArray();
      const total = countResult.length > 0 ? countResult[0].total : 0;

      const userAccounts = results.map((result) => ({
         id: result.account._id.toString(),
         userId: result._id.toString(),
         firstName: result.firstName,
         lastName: result.lastName,
         email: result.email,
         accountNumber: result.account.accountNumber,
         accountType: result.account.accountType,
         availableBalance: result.account.availableBalance,
         totalBalance: result.account.totalBalance,
         currency: result.account.currency,
         accountStatus: result.account.accountStatus,
         verificationStatus: result.verificationStatus,
         dateOpened: result.account.dateOpened,
      }));

      return { success: true, accounts: userAccounts, total };
   } catch (error) {
      console.error("Error getting all user accounts:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Manually adjust account balance
 */
export async function adjustAccountBalance(
   userId: string,
   amount: number,
   type: "credit" | "debit",
   adminId: string,
   reason?: string
): Promise<{ success: boolean; account?: AccountDetails; message?: string }> {
   try {
      const accountsCollection = await getCollection<Account>("accounts");

      const account = await accountsCollection.findOne({
         userId: new ObjectId(userId),
      });

      if (!account) {
         return { success: false, message: "Account not found" };
      }

      const oldBalance = account.availableBalance;
      const adjustment = type === "credit" ? amount : -amount;
      const newBalance = oldBalance + adjustment;

      if (newBalance < 0) {
         return {
            success: false,
            message: "Insufficient balance for debit adjustment",
         };
      }

      const result = await accountsCollection.findOneAndUpdate(
         { userId: new ObjectId(userId) },
         {
            $set: {
               availableBalance: newBalance,
               totalBalance: newBalance,
               updatedAt: new Date(),
            },
         },
         { returnDocument: "after" }
      );

      if (!result) {
         return { success: false, message: "Failed to update account balance" };
      }

      // Log the action
      await logAdminAction({
         adminId,
         action: ADMIN_ACTIONS.BALANCE_ADJUSTED,
         targetType: "account",
         targetId: result._id!.toString(),
         details: {
            before: { balance: oldBalance },
            after: { balance: newBalance },
            reason,
            metadata: { adjustment, type },
         },
      });

      const accountDetails: AccountDetails = {
         id: result._id!.toString(),
         userId: result.userId.toString(),
         accountNumber: result.accountNumber,
         routingNumber: result.routingNumber,
         accountType: result.accountType,
         accountStatus: result.accountStatus,
         availableBalance: result.availableBalance,
         totalBalance: result.totalBalance,
         currency: result.currency,
         dateOpened: result.dateOpened,
         branch: result.branch,
      };

      return { success: true, account: accountDetails };
   } catch (error) {
      console.error("Error adjusting account balance:", error);
      return { success: false, message: "Internal server error" };
   }
}

// ============================================================================
// TRANSACTION MANAGEMENT
// ============================================================================

/**
 * Get all transactions with pagination and filtering
 */
export async function getAllTransactions(options: {
   page?: number;
   limit?: number;
   status?: "pending" | "completed" | "failed" | "cancelled";
   type?: "credit" | "debit";
   userId?: string;
   dateFrom?: Date;
   dateTo?: Date;
}): Promise<{
   success: boolean;
   transactions?: TransactionDetails[];
   total?: number;
   message?: string;
}> {
   try {
      const transactionsCollection = await getCollection<Transaction>(
         "transactions"
      );
      const {
         page = 1,
         limit = 20,
         status,
         type,
         userId,
         dateFrom,
         dateTo,
      } = options;
      const skip = (page - 1) * limit;

      // Build query
      const query: any = {};

      if (status) {
         query.status = status;
      }

      if (type) {
         query.type = type;
      }

      if (userId) {
         query.userId = new ObjectId(userId);
      }

      if (dateFrom || dateTo) {
         query.createdAt = {};
         if (dateFrom) query.createdAt.$gte = dateFrom;
         if (dateTo) query.createdAt.$lte = dateTo;
      }

      const [transactions, total] = await Promise.all([
         transactionsCollection
            .find(query)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .toArray(),
         transactionsCollection.countDocuments(query),
      ]);

      const transactionDetails: TransactionDetails[] = transactions.map(
         (transaction) => ({
            id: transaction._id!.toString(),
            userId: transaction.userId.toString(),
            accountId: transaction.accountId.toString(),
            transactionId: transaction.transactionId,
            type: transaction.type,
            amount: transaction.amount,
            currency: transaction.currency,
            description: transaction.description,
            merchant: transaction.merchant,
            category: transaction.category,
            status: transaction.status,
            reference: transaction.reference,
            balanceAfter: transaction.balanceAfter,
            metadata: transaction.metadata
               ? {
                    ...transaction.metadata,
                    cardId: transaction.metadata.cardId?.toString(),
                 }
               : undefined,
            date: transaction.createdAt,
         })
      );

      return { success: true, transactions: transactionDetails, total };
   } catch (error) {
      console.error("Error getting all transactions:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Update transaction status
 */
export async function updateTransactionStatus(
   transactionId: string,
   status: "pending" | "completed" | "failed" | "cancelled",
   adminId: string,
   reason?: string
): Promise<{
   success: boolean;
   transaction?: TransactionDetails;
   message?: string;
}> {
   try {
      // Get the old status for logging
      const transactionsCollection = await getCollection<Transaction>(
         "transactions"
      );
      const transaction = await transactionsCollection.findOne({
         _id: new ObjectId(transactionId),
      });

      if (!transaction) {
         return { success: false, message: "Transaction not found" };
      }

      const oldStatus = transaction.status;

      // Use the transaction service function that handles balance updates
      const result = await updateTransactionStatusWithBalance(
         transactionId,
         status
      );

      if (!result.success) {
         return result;
      }

      // Log the action
      await logAdminAction({
         adminId,
         action: ADMIN_ACTIONS.TRANSACTION_STATUS_CHANGED,
         targetType: "transaction",
         targetId: transactionId,
         details: {
            before: { status: oldStatus },
            after: { status },
            reason,
         },
      });

      return result;
   } catch (error) {
      console.error("Error updating transaction status:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Get pending transactions count
 */
export async function getPendingTransactionsCount(): Promise<{
   success: boolean;
   count?: number;
   message?: string;
}> {
   try {
      const transactionsCollection = await getCollection<Transaction>(
         "transactions"
      );

      const count = await transactionsCollection.countDocuments({
         status: "pending",
      });

      return { success: true, count };
   } catch (error) {
      console.error("Error getting pending transactions count:", error);
      return { success: false, message: "Internal server error" };
   }
}

// ============================================================================
// NOTIFICATION MESSAGE MANAGEMENT
// ============================================================================

/**
 * Get all notification messages
 */
export async function getAllNotificationMessages(): Promise<{
   success: boolean;
   messages?: NotificationMessageDetails[];
   message?: string;
}> {
   try {
      const messagesCollection = await getCollection<NotificationMessage>(
         "notification_messages"
      );

      const messages = await messagesCollection
         .find({})
         .sort({ category: 1, key: 1 })
         .toArray();

      const messageDetails: NotificationMessageDetails[] = messages.map(
         (msg) => ({
            id: msg._id!.toString(),
            key: msg.key,
            title: msg.title,
            message: msg.message,
            type: msg.type,
            category: msg.category,
            variables: msg.variables,
            isActive: msg.isActive,
            createdAt: msg.createdAt,
            updatedAt: msg.updatedAt,
            createdBy: msg.createdBy.toString(),
            lastModifiedBy: msg.lastModifiedBy.toString(),
         })
      );

      return { success: true, messages: messageDetails };
   } catch (error) {
      console.error("Error getting notification messages:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Create notification message
 */
export async function createNotificationMessage(
   data: CreateNotificationMessageData
): Promise<{
   success: boolean;
   message?: NotificationMessageDetails;
   error?: string;
}> {
   try {
      const messagesCollection = await getCollection<NotificationMessage>(
         "notification_messages"
      );

      // Check if key already exists
      const existing = await messagesCollection.findOne({ key: data.key });
      if (existing) {
         return { success: false, error: "Message key already exists" };
      }

      const newMessage: OptionalId<NotificationMessage> = {
         key: data.key,
         title: data.title,
         message: data.message,
         type: data.type,
         category: data.category,
         variables: data.variables,
         isActive: data.isActive ?? true,
         createdAt: new Date(),
         updatedAt: new Date(),
         createdBy: new ObjectId(data.createdBy),
         lastModifiedBy: new ObjectId(data.createdBy),
      };

      const result = await messagesCollection.insertOne(newMessage);

      // Log the action
      await logAdminAction({
         adminId: data.createdBy,
         action: ADMIN_ACTIONS.NOTIFICATION_MESSAGE_CREATED,
         targetType: "notification",
         targetId: result.insertedId.toString(),
         details: {
            after: { key: data.key, title: data.title },
         },
      });

      const messageDetails: NotificationMessageDetails = {
         id: result.insertedId.toString(),
         key: data.key,
         title: data.title,
         message: data.message,
         type: data.type,
         category: data.category,
         variables: data.variables,
         isActive: data.isActive ?? true,
         createdAt: newMessage.createdAt,
         updatedAt: newMessage.updatedAt,
         createdBy: data.createdBy,
         lastModifiedBy: data.createdBy,
      };

      return { success: true, message: messageDetails };
   } catch (error) {
      console.error("Error creating notification message:", error);
      return { success: false, error: "Internal server error" };
   }
}

// ============================================================================
// AUDIT LOGGING
// ============================================================================

/**
 * Log admin action for audit trail
 */
export async function logAdminAction(data: CreateAuditLogData): Promise<void> {
   try {
      const auditCollection = await getCollection<AdminAuditLog>(
         "admin_audit_logs"
      );

      const auditLog: OptionalId<AdminAuditLog> = {
         adminId: new ObjectId(data.adminId),
         action: data.action,
         targetType: data.targetType,
         targetId: data.targetId ? new ObjectId(data.targetId) : undefined,
         details: data.details,
         ipAddress: data.ipAddress,
         userAgent: data.userAgent,
         createdAt: new Date(),
      };

      await auditCollection.insertOne(auditLog);
   } catch (error) {
      console.error("Error logging admin action:", error);
      // Don't throw error as this shouldn't break the main operation
   }
}
