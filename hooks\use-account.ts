"use client";

import { getUserAccount, getUserAccounts, updateBalance } from "@/lib/actions";
import { UpdateBalanceData } from "@/lib/models";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

/**
 * Hook to get user's primary account
 */
export function useUserAccount(userId: string | undefined) {
   return useQuery({
      queryKey: ["account", "primary", userId],
      queryFn: async () => {
         if (!userId) throw new Error("User ID is required");
         const result = await getUserAccount(userId);
         if (!result.success) {
            throw new Error(result.message || "Failed to get account");
         }
         return result.account!;
      },
      enabled: !!userId,
      staleTime: 2 * 60 * 1000, // 2 minutes
   });
}

/**
 * Hook to get all user accounts
 */
export function useUserAccounts(userId: string | undefined) {
   return useQuery({
      queryKey: ["accounts", userId],
      queryFn: async () => {
         if (!userId) throw new Error("User ID is required");
         const result = await getUserAccounts(userId);
         if (!result.success) {
            throw new Error(result.message || "Failed to get accounts");
         }
         return result.accounts || [];
      },
      enabled: !!userId,
      staleTime: 2 * 60 * 1000, // 2 minutes
   });
}

/**
 * Hook to update account balance
 */
export function useUpdateBalance() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async (data: UpdateBalanceData) => {
         const result = await updateBalance(data);
         if (!result.success) {
            throw new Error(result.message || "Failed to update balance");
         }
         return result.newBalance!;
      },
      onSuccess: () => {
         // Invalidate account queries
         queryClient.invalidateQueries({ queryKey: ["account"] });
         queryClient.invalidateQueries({ queryKey: ["accounts"] });
         // Also invalidate transactions as balance changes affect transaction history
         queryClient.invalidateQueries({ queryKey: ["transactions"] });
      },
   });
}
