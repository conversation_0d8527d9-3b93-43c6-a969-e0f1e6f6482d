/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth, useUpdateUserVerification, useUsers } from "@/hooks";
import { motion } from "framer-motion";
import {
   AlertTriangle,
   CheckCircle,
   Clock,
   Filter,
   Shield,
   ShieldCheck,
   ShieldX,
   User,
   XCircle,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function UserVerification() {
   const { user } = useAuth();
   const [searchTerm, setSearchTerm] = useState("");
   const [statusFilter, setStatusFilter] = useState<
      "all" | "verified" | "pending" | "unverified"
   >("all");
   const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
   const [bulkAction, setBulkAction] = useState<
      "verified" | "pending" | "unverified" | ""
   >("");
   const [bulkReason, setBulkReason] = useState("");

   const updateVerificationMutation = useUpdateUserVerification();

   // Get users with filtering
   const {
      data: usersData,
      isLoading,
      error,
      refetch,
   } = useUsers({
      search: searchTerm,
      verificationStatus: statusFilter === "all" ? undefined : statusFilter,
      limit: 50,
   });

   const users = usersData?.users || [];

   const handleStatusChange = async (
      userId: string,
      newStatus: "verified" | "pending" | "unverified",
      reason?: string
   ) => {
      if (!user) return;

      try {
         await updateVerificationMutation.mutateAsync({
            userId,
            verificationStatus: newStatus,
            adminId: user.id,
            reason,
         });

         toast.success(`User verification status updated to ${newStatus}`);
      } catch (error) {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to update verification status"
         );
      }
   };

   const handleBulkAction = async () => {
      if (!bulkAction || selectedUsers.length === 0 || !user) {
         toast.error("Please select users and an action");
         return;
      }

      if (!bulkReason.trim()) {
         toast.error("Please provide a reason for the bulk action");
         return;
      }

      try {
         // Process bulk updates
         const promises = selectedUsers.map((userId) =>
            updateVerificationMutation.mutateAsync({
               userId,
               verificationStatus: bulkAction,
               adminId: user.id,
               reason: bulkReason,
            })
         );

         await Promise.all(promises);

         toast.success(
            `${selectedUsers.length} users updated to ${bulkAction} status`
         );
         setSelectedUsers([]);
         setBulkAction("");
         setBulkReason("");
      } catch (error) {
         console.error("Bulk action error:", error);
         toast.error("Failed to perform bulk action");
      }
   };

   const handleSelectUser = (userId: string, checked: boolean) => {
      if (checked) {
         setSelectedUsers((prev) => [...prev, userId]);
      } else {
         setSelectedUsers((prev) => prev.filter((id) => id !== userId));
      }
   };

   const handleSelectAll = (checked: boolean) => {
      if (checked) {
         setSelectedUsers(users.map((user) => user.id));
      } else {
         setSelectedUsers([]);
      }
   };

   const getStatusIcon = (status: string) => {
      switch (status) {
         case "verified":
            return <ShieldCheck className="h-4 w-4 text-green-600" />;
         case "pending":
            return <Clock className="h-4 w-4 text-yellow-600" />;
         case "unverified":
            return <ShieldX className="h-4 w-4 text-red-600" />;
         default:
            return <Shield className="h-4 w-4 text-gray-600" />;
      }
   };

   const getStatusBadge = (status: string) => {
      const variants = {
         verified: {
            variant: "default" as const,
            className: "bg-green-100 text-green-800 border-green-200",
         },
         pending: {
            variant: "secondary" as const,
            className: "bg-yellow-100 text-yellow-800 border-yellow-200",
         },
         unverified: {
            variant: "destructive" as const,
            className: "bg-red-100 text-red-800 border-red-200",
         },
      };

      const config =
         variants[status as keyof typeof variants] || variants.unverified;

      return (
         <Badge variant={config.variant} className={config.className}>
            {getStatusIcon(status)}
            <span className="ml-1 capitalize">{status}</span>
         </Badge>
      );
   };

   const filteredUsers = users.filter((user) => {
      const matchesSearch =
         user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
         user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
         user.email.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesSearch;
   });

   const stats = {
      total: users.length,
      verified: users.filter((u) => u.verificationStatus === "verified").length,
      pending: users.filter((u) => u.verificationStatus === "pending").length,
      unverified: users.filter((u) => u.verificationStatus === "unverified")
         .length,
   };

   return (
      <div className="container mx-auto px-4 py-8">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
         >
            {/* Header */}
            <div className="mb-8">
               <h1 className="text-3xl font-bold text-foreground mb-2">
                  User Verification Management
               </h1>
               <p className="text-muted-foreground">
                  Manage user verification statuses with bulk operations and
                  detailed filtering.
               </p>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <User className="h-5 w-5 text-muted-foreground" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Total Users
                           </p>
                           <p className="text-2xl font-bold">{stats.total}</p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <ShieldCheck className="h-5 w-5 text-green-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Verified
                           </p>
                           <p className="text-2xl font-bold text-green-600">
                              {stats.verified}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <Clock className="h-5 w-5 text-yellow-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Pending
                           </p>
                           <p className="text-2xl font-bold text-yellow-600">
                              {stats.pending}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <ShieldX className="h-5 w-5 text-red-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Unverified
                           </p>
                           <p className="text-2xl font-bold text-red-600">
                              {stats.unverified}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
            </div>

            {/* Filters and Search */}
            <Card className="mb-6">
               <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                     <Filter className="h-5 w-5" />
                     Filters & Search
                  </CardTitle>
               </CardHeader>
               <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                     <div>
                        <Label htmlFor="search">Search Users</Label>
                        <Input
                           id="search"
                           placeholder="Search by name or email..."
                           value={searchTerm}
                           onChange={(e) => setSearchTerm(e.target.value)}
                           className="mt-1"
                        />
                     </div>
                     <div>
                        <Label htmlFor="status-filter">
                           Verification Status
                        </Label>
                        <Select
                           value={statusFilter}
                           onValueChange={(value: any) =>
                              setStatusFilter(value)
                           }
                        >
                           <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Filter by status" />
                           </SelectTrigger>
                           <SelectContent>
                              <SelectItem value="all">All Statuses</SelectItem>
                              <SelectItem value="verified">Verified</SelectItem>
                              <SelectItem value="pending">Pending</SelectItem>
                              <SelectItem value="unverified">
                                 Unverified
                              </SelectItem>
                           </SelectContent>
                        </Select>
                     </div>
                  </div>
               </CardContent>
            </Card>

            {/* Bulk Actions */}
            {selectedUsers.length > 0 && (
               <Card className="mb-6">
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5" />
                        Bulk Actions ({selectedUsers.length} selected)
                     </CardTitle>
                  </CardHeader>
                  <CardContent>
                     <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                           <Label htmlFor="bulk-action">Action</Label>
                           <Select
                              value={bulkAction}
                              onValueChange={(value: any) =>
                                 setBulkAction(value)
                              }
                           >
                              <SelectTrigger className="mt-1">
                                 <SelectValue placeholder="Select action" />
                              </SelectTrigger>
                              <SelectContent>
                                 <SelectItem value="verified">
                                    Mark as Verified
                                 </SelectItem>
                                 <SelectItem value="pending">
                                    Mark as Pending
                                 </SelectItem>
                                 <SelectItem value="unverified">
                                    Mark as Unverified
                                 </SelectItem>
                              </SelectContent>
                           </Select>
                        </div>
                        <div>
                           <Label htmlFor="bulk-reason">Reason</Label>
                           <Input
                              id="bulk-reason"
                              placeholder="Reason for bulk action..."
                              value={bulkReason}
                              onChange={(e) => setBulkReason(e.target.value)}
                              className="mt-1"
                           />
                        </div>
                        <div className="flex items-end">
                           <Button
                              onClick={handleBulkAction}
                              disabled={
                                 !bulkAction ||
                                 !bulkReason.trim() ||
                                 updateVerificationMutation.isPending
                              }
                              className="w-full"
                           >
                              Apply to {selectedUsers.length} users
                           </Button>
                        </div>
                     </div>
                  </CardContent>
               </Card>
            )}

            {/* Users List */}
            <Card>
               <CardHeader>
                  <div className="flex items-center justify-between">
                     <CardTitle>Users ({filteredUsers.length})</CardTitle>
                     <div className="flex items-center gap-2">
                        <Checkbox
                           checked={
                              selectedUsers.length === filteredUsers.length &&
                              filteredUsers.length > 0
                           }
                           onCheckedChange={handleSelectAll}
                        />
                        <Label className="text-sm">Select All</Label>
                     </div>
                  </div>
               </CardHeader>
               <CardContent>
                  {isLoading ? (
                     <div className="space-y-4">
                        {Array.from({ length: 5 }).map((_, index) => (
                           <div
                              key={index}
                              className="flex items-center gap-4 p-4 border rounded-lg"
                           >
                              <Skeleton className="h-4 w-4" />
                              <Skeleton className="h-10 w-10 rounded-full" />
                              <div className="flex-1 space-y-2">
                                 <Skeleton className="h-4 w-48" />
                                 <Skeleton className="h-3 w-32" />
                              </div>
                              <Skeleton className="h-6 w-20" />
                              <Skeleton className="h-8 w-24" />
                           </div>
                        ))}
                     </div>
                  ) : error ? (
                     <div className="text-center py-8">
                        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                        <p className="text-red-600 mb-4">
                           Failed to load users
                        </p>
                        <Button onClick={() => refetch()} variant="outline">
                           Try Again
                        </Button>
                     </div>
                  ) : filteredUsers.length === 0 ? (
                     <div className="text-center py-8">
                        <User className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground">No users found</p>
                     </div>
                  ) : (
                     <div className="space-y-4">
                        {filteredUsers.map((user) => (
                           <div
                              key={user.id}
                              className="flex items-center gap-4 p-4 border rounded-lg hover:bg-muted/30 transition-colors"
                           >
                              <Checkbox
                                 checked={selectedUsers.includes(user.id)}
                                 onCheckedChange={(checked) =>
                                    handleSelectUser(
                                       user.id,
                                       checked as boolean
                                    )
                                 }
                              />
                              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                                 <User className="w-5 h-5 text-primary" />
                              </div>
                              <div className="flex-1">
                                 <div className="flex items-center gap-2">
                                    <p className="font-medium">
                                       {user.firstName} {user.lastName}
                                    </p>
                                    {getStatusBadge(user.verificationStatus)}
                                 </div>
                                 <p className="text-sm text-muted-foreground">
                                    {user.email}
                                 </p>
                                 <p className="text-xs text-muted-foreground">
                                    Role: {user.role} • Account:{" "}
                                    {user.accountType}
                                 </p>
                              </div>
                              <div className="flex gap-2">
                                 <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-green-600 border-green-200 hover:bg-green-50"
                                    onClick={() =>
                                       handleStatusChange(
                                          user.id,
                                          "verified",
                                          "Manual verification by admin"
                                       )
                                    }
                                    disabled={
                                       user.verificationStatus === "verified" ||
                                       updateVerificationMutation.isPending
                                    }
                                 >
                                    <CheckCircle className="w-4 h-4 mr-1" />
                                    Verify
                                 </Button>
                                 <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-yellow-600 border-yellow-200 hover:bg-yellow-50"
                                    onClick={() =>
                                       handleStatusChange(
                                          user.id,
                                          "pending",
                                          "Set to pending by admin"
                                       )
                                    }
                                    disabled={
                                       user.verificationStatus === "pending" ||
                                       updateVerificationMutation.isPending
                                    }
                                 >
                                    <Clock className="w-4 h-4 mr-1" />
                                    Pending
                                 </Button>
                                 <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-red-600 border-red-200 hover:bg-red-50"
                                    onClick={() =>
                                       handleStatusChange(
                                          user.id,
                                          "unverified",
                                          "Unverified by admin"
                                       )
                                    }
                                    disabled={
                                       user.verificationStatus ===
                                          "unverified" ||
                                       updateVerificationMutation.isPending
                                    }
                                 >
                                    <XCircle className="w-4 h-4 mr-1" />
                                    Unverify
                                 </Button>
                              </div>
                           </div>
                        ))}
                     </div>
                  )}
               </CardContent>
            </Card>
         </motion.div>
      </div>
   );
}
