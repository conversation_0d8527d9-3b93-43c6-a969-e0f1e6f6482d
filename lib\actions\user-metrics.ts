"use server";

import { revalidatePath } from "next/cache";
import { UpdateUserMetricsData, UserMetricsDetails } from "@/lib/models";
import { getUserMetrics, upsertUserMetrics } from "@/lib/services/user-metrics";

/**
 * Server Action: Get user metrics
 */
export async function getUserMetricsAction(
   userId: string
): Promise<{ success: boolean; metrics?: UserMetricsDetails; message?: string }> {
   try {
      const result = await getUserMetrics(userId);
      return result;
   } catch (error) {
      console.error("Get user metrics action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Update user metrics
 */
export async function updateUserMetricsAction(
   userId: string,
   data: UpdateUserMetricsData
): Promise<{ success: boolean; metrics?: UserMetricsDetails; message?: string }> {
   try {
      const result = await upsertUserMetrics(userId, data);
      
      if (result.success) {
         // Revalidate relevant paths
         revalidatePath("/dashboard");
         revalidatePath(`/admin/users/${userId}`);
         revalidatePath("/admin/users");
      }
      
      return result;
   } catch (error) {
      console.error("Update user metrics action error:", error);
      return { success: false, message: "Internal server error" };
   }
}
