"use client";

import { motion } from "framer-motion";
import { Shield } from "lucide-react";
import Image from "next/image";

export default function RootLayout({
   children,
}: Readonly<{
   children: React.ReactNode;
}>) {
   return (
      <div className="min-h-screen flex">
         {/* Left side - Hero section */}
         <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden" />

         <motion.div
            className="lg:w-1/2 bg-gradient-to-br from-blue-900 via-blue-800 to-blue-950 p-8 lg:p-12 flex flex-col justify-center fixed top-0 bottom-0 overflow-hidden"
            initial={{ x: -100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6 }}
         >
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
               <div className="absolute top-20 left-20 w-32 h-32 border border-white rounded-full"></div>
               <div className="absolute bottom-20 right-20 w-40 h-40 border border-white rounded-full"></div>
               <div className="absolute top-1/2 left-1/4 w-24 h-24 border border-white rounded-full"></div>
            </div>

            <div className="relative z-10">
               <motion.div
                  className="flex items-center space-x-3 mb-8"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
               >
                  <Image
                     src="/images/logo-white.svg"
                     alt="Paramount Bank Logo"
                     width={100}
                     height={100}
                     className="h-20 w-auto"
                  />
               </motion.div>

               <motion.div
                  className="space-y-6"
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.6 }}
               >
                  <h2 className="text-3xl lg:text-4xl font-bold text-white leading-tight">
                     Banking Made Simple, Secure & Accessible
                  </h2>
                  <p className="text-blue-100 text-lg leading-relaxed">
                     Experience the future of banking with our comprehensive
                     digital platform. Secure transactions, real-time insights,
                     and 24/7 access to your finances.
                  </p>

                  <div className="space-y-4 pt-4">
                     <div className="flex items-center space-x-3">
                        <Shield className="w-5 h-5 text-amber-400" />
                        <span className="text-blue-100">
                           Bank-grade security
                        </span>
                     </div>
                     <div className="flex items-center space-x-3">
                        <Shield className="w-5 h-5 text-amber-400" />
                        <span className="text-blue-100">
                           FDIC insured accounts
                        </span>
                     </div>
                     <div className="flex items-center space-x-3">
                        <Shield className="w-5 h-5 text-amber-400" />
                        <span className="text-blue-100">
                           24/7 fraud monitoring
                        </span>
                     </div>
                  </div>
               </motion.div>
            </div>
         </motion.div>

         {/* <div className="hidden lg:flex lg:w-1/2 bg-gradient-hero fixed top-0 bottom-0 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/90 to-primary-dark/90" />
            <div className="relative z-10 flex flex-col justify-center px-12 text-primary-foreground">
               <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
               >
                  <BankLogo size="lg" />
                  <h2 className="text-4xl font-bold mt-8 mb-4">
                     Secure Banking
                  </h2>
                  <p className="text-xl opacity-90 mb-8">
                     Experience next-generation digital banking with
                     military-grade security and intuitive design.
                  </p>
                  <div className="flex items-center gap-3 text-accent">
                     <Shield size={24} />
                     <span className="text-lg">
                        Bank-level encryption & protection
                     </span>
                  </div>
               </motion.div>
            </div>
         </div> */}

         {/* Right side - Form */}
         <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
            {children}
         </div>
      </div>
   );
}
