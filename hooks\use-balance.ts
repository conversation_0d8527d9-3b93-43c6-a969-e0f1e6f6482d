"use client";

import { useAuth, useUserAccount } from "@/hooks";

/**
 * Hook to get user's account balance with loading and error states
 */
export function useBalance() {
   const { user } = useAuth();
   const { data: userAccount, isLoading, error, refetch } = useUserAccount(user?.id);

   return {
      balance: userAccount?.availableBalance || 0,
      totalBalance: userAccount?.totalBalance || 0,
      isLoading,
      error,
      refetch,
      hasAccount: !!userAccount,
   };
}

/**
 * Hook to format balance display with visibility toggle
 */
export function useBalanceDisplay() {
   const { balance, totalBalance, isLoading, error, refetch, hasAccount } = useBalance();

   const formatBalance = (amount: number, showBalance: boolean = true) => {
      if (!showBalance) return "••••••";
      return `$${amount.toLocaleString("en-US", { minimumFractionDigits: 2 })}`;
   };

   return {
      balance,
      totalBalance,
      isLoading,
      error,
      refetch,
      hasAccount,
      formatBalance,
   };
}
