"use client";

import { ProgressIndicator } from "@/components/auth/progress-indicator";
import { But<PERSON> } from "@/components/ui/button";
import { AuthLoader } from "@/components/ui/page-loader";
import Link from "next/link";
// import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import { AnimatePresence } from "framer-motion";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
   AccountSetupStep,
   accountSetupSchema,
   type AccountSetupForm,
} from "./components/account-setup-step";
import {
   ContactStep,
   contactSchema,
   type ContactForm,
} from "./components/contact-step";
import {
   PersonalInfoStep,
   personalInfoSchema,
   type PersonalInfoForm,
} from "./components/personal-info-step";
import {
   SecurityStep,
   securitySchema,
   type SecurityForm,
} from "./components/security-step";

const steps = [
   { id: 1, title: "Personal Info", description: "Basic details" },
   { id: 2, title: "Contact", description: "Contact information" },
   { id: 3, title: "Account Setup", description: "Account preferences" },
   { id: 4, title: "Security", description: "Password & verification" },
];

export default function Signup() {
   const [currentStep, setCurrentStep] = useState(1);

   const router = useRouter();
   const {
      register,
      isAuthenticated,
      isRegistering,
      registerError,
      resetRegisterError,
      isHydrated,
   } = useAuth();
   // const { toast } = useToast();

   // Form data storage
   const [formData, setFormData] = useState({
      personalInfo: {} as PersonalInfoForm,
      contact: {} as ContactForm,
      accountSetup: {} as AccountSetupForm,
      security: {} as SecurityForm,
   });

   // Form hooks for each step
   const personalInfoForm = useForm<PersonalInfoForm>({
      resolver: zodResolver(personalInfoSchema),
      defaultValues: formData.personalInfo,
   });

   const contactForm = useForm<ContactForm>({
      resolver: zodResolver(contactSchema),
      defaultValues: formData.contact,
   });

   const accountSetupForm = useForm<AccountSetupForm>({
      resolver: zodResolver(accountSetupSchema),
      defaultValues: formData.accountSetup,
   });

   const securityForm = useForm<SecurityForm>({
      resolver: zodResolver(securitySchema),
      defaultValues: formData.security,
   });

   // Redirect if already authenticated
   useEffect(() => {
      // Only redirect after hydration to prevent SSR/client mismatch
      if (!isHydrated) return;

      if (isAuthenticated) {
         router.push("/pin-verification");
      }
   }, [isAuthenticated, isHydrated, router]);

   // Show loading while checking authentication or redirecting
   if (!isHydrated || isAuthenticated) {
      return <AuthLoader />;
   }

   const nextStep = async () => {
      let isValid = false;

      switch (currentStep) {
         case 1:
            isValid = await personalInfoForm.trigger();
            if (isValid) {
               setFormData((prev) => ({
                  ...prev,
                  personalInfo: personalInfoForm.getValues(),
               }));
            }
            break;
         case 2:
            isValid = await contactForm.trigger();
            if (isValid) {
               setFormData((prev) => ({
                  ...prev,
                  contact: contactForm.getValues(),
               }));
            }
            break;
         case 3:
            isValid = await accountSetupForm.trigger();
            if (isValid) {
               setFormData((prev) => ({
                  ...prev,
                  accountSetup: accountSetupForm.getValues(),
               }));
            }
            break;
         case 4:
            isValid = await securityForm.trigger();
            if (isValid) {
               setFormData((prev) => ({
                  ...prev,
                  security: securityForm.getValues(),
               }));
               await handleFinalSubmit();
               return;
            }
            break;
      }

      if (isValid && currentStep < 4) {
         setCurrentStep(currentStep + 1);
      }
   };

   const prevStep = () => {
      if (currentStep > 1) {
         setCurrentStep(currentStep - 1);
      }
   };

   const handleFinalSubmit = async () => {
      try {
         resetRegisterError(); // Clear any previous errors
         const completeData = {
            ...formData.personalInfo,
            ...formData.contact,
            ...formData.accountSetup,
            email: formData.contact.email,
            password: formData.security.password,
            transactionPin: formData.accountSetup.transactionPin,
         };

         await register(completeData);
         // toast({
         //    title: "Account created successfully!",
         //    description: "Welcome to Paramount Bank. You're now signed in.",
         // });
         router.push("/pin-verification");
      } catch (error) {
         console.error(error);
         // Error is handled by the useAuth hook
         // toast({
         //    title: "Registration failed",
         //    description: registerError || "An unexpected error occurred. Please try again.",
         //    variant: "destructive",
         // });
      }
   };

   const renderStep = () => {
      switch (currentStep) {
         case 1:
            return <PersonalInfoStep form={personalInfoForm} />;

         case 2:
            return <ContactStep form={contactForm} />;

         case 3:
            return <AccountSetupStep form={accountSetupForm} />;

         case 4:
            return <SecurityStep form={securityForm} />;

         default:
            return null;
      }
   };

   return (
      <div className="bg-background">
         <div className="container mx-auto px-4 py-8">
            <ProgressIndicator steps={steps} currentStep={currentStep} />

            <div className="max-w-2xl mx-auto">
               <div className="bank-card p-8">
                  <AnimatePresence mode="wait">{renderStep()}</AnimatePresence>

                  {registerError && currentStep === 4 && (
                     <div className="p-3 rounded-lg bg-destructive/10 border border-destructive/20 mt-4">
                        <p className="text-sm text-destructive">
                           {registerError}
                        </p>
                     </div>
                  )}

                  <div className="flex justify-between mt-8">
                     <Button
                        variant="outline"
                        onClick={prevStep}
                        disabled={currentStep === 1}
                        className="flex items-center gap-2"
                     >
                        <ArrowLeft size={16} />
                        Back
                     </Button>

                     <Button
                        variant="premium"
                        onClick={nextStep}
                        disabled={isRegistering}
                        className="flex items-center gap-2"
                     >
                        {currentStep === 4 ? (
                           isRegistering ? (
                              "Creating Account..."
                           ) : (
                              "Create Account"
                           )
                        ) : (
                           <>
                              Next
                              <ArrowRight size={16} />
                           </>
                        )}
                     </Button>
                  </div>

                  <div className="mt-8 text-center">
                     <p className="text-muted-foreground">
                        Already have an account?{" "}
                        <Link
                           href="/login"
                           className="text-primary hover:text-primary-light font-medium transition-colors"
                        >
                           Sign In
                        </Link>
                     </p>
                  </div>
               </div>
            </div>
         </div>
      </div>
   );
}
