"use client";

import { Input } from "@/components/ui/input";
import { motion } from "framer-motion";
import { CircleUserRound } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";

// Step 1 Schema
export const personalInfoSchema = z.object({
   firstName: z.string().min(2, "First name must be at least 2 characters"),
   middleName: z.string().optional(),
   lastName: z.string().min(2, "Last name must be at least 2 characters"),
   username: z.string().min(3, "Username must be at least 3 characters"),
});

export type PersonalInfoForm = z.infer<typeof personalInfoSchema>;

interface PersonalInfoStepProps {
   form: UseFormReturn<PersonalInfoForm>;
}

export function PersonalInfoStep({ form }: PersonalInfoStepProps) {
   return (
      <motion.div
         key="step1"
         initial={{ opacity: 0, x: 20 }}
         animate={{ opacity: 1, x: 0 }}
         exit={{ opacity: 0, x: -20 }}
         className="space-y-6"
      >
         <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-foreground mb-2">
               Personal Information
            </h2>
            <p className="text-muted-foreground">
               Tell us about yourself
            </p>
         </div>

         <Input
            label="First Name"
            placeholder="Enter your first name"
            icon={<CircleUserRound size={18} />}
            error={form.formState.errors.firstName?.message}
            {...form.register("firstName")}
         />

         <Input
            label="Middle Name (Optional)"
            placeholder="Enter your middle name"
            icon={<CircleUserRound size={18} />}
            error={form.formState.errors.middleName?.message}
            {...form.register("middleName")}
         />

         <Input
            label="Last Name"
            placeholder="Enter your last name"
            icon={<CircleUserRound size={18} />}
            error={form.formState.errors.lastName?.message}
            {...form.register("lastName")}
         />

         <Input
            label="Username"
            placeholder="Choose a unique username"
            icon={<CircleUserRound size={18} />}
            error={form.formState.errors.username?.message}
            {...form.register("username")}
         />
      </motion.div>
   );
}
