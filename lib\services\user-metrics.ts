import {
   CreateUserMetricsData,
   UpdateUserMetricsData,
   UserMetrics,
   UserMetricsDetails,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { ObjectId, OptionalId } from "mongodb";

/**
 * Get user metrics by user ID
 */
export async function getUserMetrics(
   userId: string
): Promise<{ success: boolean; metrics?: UserMetricsDetails; message?: string }> {
   try {
      const metricsCollection = await getCollection<UserMetrics>("user_metrics");

      const metrics = await metricsCollection.findOne({
         userId: new ObjectId(userId),
      });

      if (!metrics) {
         // Return default metrics if none exist
         const defaultMetrics: UserMetricsDetails = {
            id: "",
            userId,
            monthlySpending: 0,
            monthlyIncome: 0,
            transactionCount: 0,
            spendingChange: 0,
            incomeChange: 0,
            lastUpdated: new Date(),
         };
         return { success: true, metrics: defaultMetrics };
      }

      const metricsDetails: UserMetricsDetails = {
         id: metrics._id!.toString(),
         userId: metrics.userId.toString(),
         monthlySpending: metrics.monthlySpending,
         monthlyIncome: metrics.monthlyIncome,
         transactionCount: metrics.transactionCount,
         spendingChange: metrics.spendingChange,
         incomeChange: metrics.incomeChange,
         lastUpdated: metrics.lastUpdated,
         updatedBy: metrics.updatedBy?.toString(),
      };

      return { success: true, metrics: metricsDetails };
   } catch (error) {
      console.error("Error getting user metrics:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Create or update user metrics
 */
export async function upsertUserMetrics(
   userId: string,
   data: UpdateUserMetricsData
): Promise<{ success: boolean; metrics?: UserMetricsDetails; message?: string }> {
   try {
      const metricsCollection = await getCollection<UserMetrics>("user_metrics");

      const updateData = {
         ...data,
         updatedBy: data.updatedBy ? new ObjectId(data.updatedBy) : undefined,
         lastUpdated: new Date(),
         updatedAt: new Date(),
      };

      const result = await metricsCollection.findOneAndUpdate(
         { userId: new ObjectId(userId) },
         {
            $set: updateData,
            $setOnInsert: {
               userId: new ObjectId(userId),
               createdAt: new Date(),
            },
         },
         { 
            upsert: true, 
            returnDocument: "after" 
         }
      );

      if (!result) {
         return { success: false, message: "Failed to update user metrics" };
      }

      const metricsDetails: UserMetricsDetails = {
         id: result._id!.toString(),
         userId: result.userId.toString(),
         monthlySpending: result.monthlySpending,
         monthlyIncome: result.monthlyIncome,
         transactionCount: result.transactionCount,
         spendingChange: result.spendingChange,
         incomeChange: result.incomeChange,
         lastUpdated: result.lastUpdated,
         updatedBy: result.updatedBy?.toString(),
      };

      return { success: true, metrics: metricsDetails };
   } catch (error) {
      console.error("Error upserting user metrics:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Create initial user metrics
 */
export async function createUserMetrics(
   data: CreateUserMetricsData
): Promise<{ success: boolean; metrics?: UserMetricsDetails; message?: string }> {
   try {
      const metricsCollection = await getCollection<UserMetrics>("user_metrics");

      // Check if metrics already exist
      const existingMetrics = await metricsCollection.findOne({
         userId: new ObjectId(data.userId),
      });

      if (existingMetrics) {
         return { success: false, message: "User metrics already exist" };
      }

      const newMetrics: OptionalId<UserMetrics> = {
         userId: new ObjectId(data.userId),
         monthlySpending: data.monthlySpending,
         monthlyIncome: data.monthlyIncome,
         transactionCount: data.transactionCount,
         spendingChange: data.spendingChange || 0,
         incomeChange: data.incomeChange || 0,
         lastUpdated: new Date(),
         updatedBy: data.updatedBy ? new ObjectId(data.updatedBy) : undefined,
         createdAt: new Date(),
         updatedAt: new Date(),
      };

      const result = await metricsCollection.insertOne(newMetrics);

      if (result.insertedId) {
         const metricsDetails: UserMetricsDetails = {
            id: result.insertedId.toString(),
            userId: data.userId,
            monthlySpending: data.monthlySpending,
            monthlyIncome: data.monthlyIncome,
            transactionCount: data.transactionCount,
            spendingChange: data.spendingChange || 0,
            incomeChange: data.incomeChange || 0,
            lastUpdated: new Date(),
            updatedBy: data.updatedBy,
         };

         return { success: true, metrics: metricsDetails };
      }

      return { success: false, message: "Failed to create user metrics" };
   } catch (error) {
      console.error("Error creating user metrics:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Delete user metrics
 */
export async function deleteUserMetrics(
   userId: string
): Promise<{ success: boolean; message?: string }> {
   try {
      const metricsCollection = await getCollection<UserMetrics>("user_metrics");

      const result = await metricsCollection.deleteOne({
         userId: new ObjectId(userId),
      });

      if (result.deletedCount > 0) {
         return { success: true };
      }

      return { success: false, message: "User metrics not found" };
   } catch (error) {
      console.error("Error deleting user metrics:", error);
      return { success: false, message: "Internal server error" };
   }
}
