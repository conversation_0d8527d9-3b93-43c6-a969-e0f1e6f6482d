"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
// import { useAuth } from "@/hooks";
import bankingHero from "@/public/images/hero.jpg";
import { motion } from "framer-motion";
import {
   ArrowRight,
   Award,
   Building2,
   CheckCircle,
   CreditCard,
   DollarSign,
   Lock,
   Phone,
   PiggyBank,
   Send,
   Shield,
   Star,
   TrendingUp,
   Users,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

// Features data - kept for potential future use
// const features = [
//    {
//       icon: Shield,
//       title: "Bank-Grade Security",
//       description:
//          "Your money and data are protected with military-grade encryption and advanced security protocols.",
//    },
//    {
//       icon: Zap,
//       title: "Lightning Fast",
//       description:
//          "Experience instant transfers and real-time updates on all your banking activities.",
//    },
//    {
//       icon: Globe,
//       title: "Global Access",
//       description:
//          "Access your account anywhere in the world with our comprehensive international network.",
//    },
//    {
//       icon: Award,
//       title: "Award Winning",
//       description:
//          "Recognized as the best digital banking platform for three consecutive years.",
//    },
// ];

const services = [
   {
      icon: CreditCard,
      title: "Digital Cards",
      description:
         "Virtual and physical cards with instant issuance and premium rewards.",
      features: [
         "Instant virtual cards",
         "Premium cashback rates",
         "Global acceptance",
      ],
   },
   {
      icon: Send,
      title: "International Transfers",
      description:
         "Send money worldwide with competitive rates and fast processing.",
      features: ["Real-time transfers", "Low fees", "150+ countries"],
   },
   {
      icon: PiggyBank,
      title: "Smart Savings",
      description:
         "Automated savings with high-yield accounts and investment options.",
      features: ["Auto-save features", "High interest rates", "Goal tracking"],
   },
   {
      icon: TrendingUp,
      title: "Investment Platform",
      description:
         "Professional investment tools and portfolio management services.",
      features: [
         "Expert guidance",
         "Diverse portfolios",
         "Real-time analytics",
      ],
   },
];

const trustIndicators = [
   {
      icon: Shield,
      title: "FDIC Insured",
      description: "Your deposits are protected up to $250,000",
   },
   {
      icon: Lock,
      title: "256-bit Encryption",
      description: "Military-grade security for all transactions",
   },
   {
      icon: Award,
      title: "ISO 27001 Certified",
      description: "International security management standards",
   },
   {
      icon: Users,
      title: "500K+ Customers",
      description: "Trusted by half a million users worldwide",
   },
];

const benefits = [
   {
      icon: DollarSign,
      title: "No Hidden Fees",
      description:
         "Transparent pricing with no surprise charges or monthly maintenance fees.",
   },
   {
      icon: Phone,
      title: "24/7 Support",
      description:
         "Round-the-clock customer service with real human agents, not bots.",
   },
   {
      icon: Building2,
      title: "Branch Network",
      description:
         "Access to over 1,000 partner branches and 50,000 ATMs worldwide.",
   },
   {
      icon: Star,
      title: "Premium Experience",
      description:
         "Personalized banking with dedicated relationship managers for all accounts.",
   },
];

export default function Home() {
   const [isScrolled, setIsScrolled] = useState(false);

   useEffect(() => {
      const handleScroll = () => {
         setIsScrolled(window.scrollY > 0);
      };

      window.addEventListener("scroll", handleScroll);

      return () => {
         window.removeEventListener("scroll", handleScroll);
      };
   }, []);

   return (
      <div className="min-h-screen flex flex-col relative overflow-hidden">
         {/* Skip Link for Accessibility */}
         <a
            href="#main-content"
            className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 bg-primary text-primary-foreground px-4 py-2 rounded-md"
         >
            Skip to main content
         </a>

         {/* Navigation Header */}
         <motion.header
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className={`z-20 w-full fixed ${
               isScrolled ? "bg-black/20 backdrop-blur-md" : ""
            }`}
            role="banner"
            aria-label="Site navigation"
         >
            <div className="container mx-auto px-4 py-4 sm:py-6">
               <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                     <Image
                        src="/images/logo.svg"
                        alt="Paramount Bank Logo"
                        width={100}
                        height={100}
                        className="h-8 w-auto"
                     />
                     <span className="text-lg sm:text-2xl font-bold text-white">
                        Paramount Bank
                     </span>
                  </div>
                  <div className="flex items-center space-x-2 sm:space-x-4">
                     <Button
                        asChild
                        variant="ghost"
                        size="sm"
                        className="homepage-button-touch text-white hover:bg-white/10 hover:text-white text-sm sm:text-base"
                     >
                        <Link href="/login">Sign In</Link>
                     </Button>
                     <Button
                        asChild
                        size="sm"
                        className="homepage-button-touch text-white !px-6 hover:bg-white/90 text-sm sm:text-base sm:size-lg"
                     >
                        <Link href="/signup">
                           <span className="hidden sm:inline">Get Started</span>
                           <span className="sm:hidden">Start</span>
                           <ArrowRight className="ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4" />
                        </Link>
                     </Button>
                  </div>
               </div>
            </div>
         </motion.header>

         {/* Hero Section */}
         <div className="relative">
            <div className="absolute inset-0 -z-10" aria-hidden="true">
               <Image
                  src={bankingHero}
                  fill
                  className="object-cover"
                  alt=""
                  priority
                  role="presentation"
               />
               <div className="absolute inset-0 bg-gradient-to-br from-primary/40 via-primary/30 to-accent/20"></div>
            </div>
            <main
               id="main-content"
               className="relative z-10 min-h-screen flex-1 flex items-center"
               role="main"
            >
               <motion.section
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  className="text-center text-white w-full container mx-auto px-4 py-20"
                  aria-labelledby="hero-title"
               >
                  <motion.div
                     initial={{ opacity: 0, y: 20 }}
                     animate={{ opacity: 1, y: 0 }}
                     transition={{ delay: 0.2, duration: 0.8 }}
                     className="mb-6"
                  >
                     <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-white/90 text-sm mb-6">
                        <Shield className="h-4 w-4 mr-2" />
                        FDIC Insured • 256-bit Encryption • ISO 27001 Certified
                     </div>
                  </motion.div>
                  <motion.h1
                     id="hero-title"
                     initial={{ opacity: 0, y: 20 }}
                     animate={{ opacity: 1, y: 0 }}
                     transition={{ delay: 0.3, duration: 0.8 }}
                     className="homepage-hero-title mb-6"
                  >
                     Banking
                     <span className="block text-accent">Reimagined</span>
                  </motion.h1>
                  <motion.p
                     initial={{ opacity: 0, y: 20 }}
                     animate={{ opacity: 1, y: 0 }}
                     transition={{ delay: 0.5, duration: 0.8 }}
                     className="homepage-hero-subtitle mb-8 sm:mb-12 text-white/90 max-w-4xl mx-auto px-4 sm:px-0"
                  >
                     Experience the future of digital banking with
                     military-grade security, instant transfers, and
                     personalized financial solutions designed for the modern
                     world.
                  </motion.p>
                  <motion.div
                     initial={{ opacity: 0, y: 20 }}
                     animate={{ opacity: 1, y: 0 }}
                     transition={{ delay: 0.7, duration: 0.8 }}
                     className="flex flex-col sm:flex-row gap-4 justify-center items-center px-4 sm:px-0"
                  >
                     <Button
                        asChild
                        size="xl"
                        className="homepage-button-touch text-white hover:bg-white/90 shadow-2xl w-full sm:w-auto"
                     >
                        <Link href="/signup">
                           Open Your Account
                           <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                     </Button>
                     <Button
                        asChild
                        variant="outline"
                        size="xl"
                        className="homepage-button-touch border border-white/30 bg-white/10 backdrop-blur-sm text-white hover:bg-white hover:text-primary w-full sm:w-auto"
                     >
                        <Link href="/login">Sign In to Your Account</Link>
                     </Button>
                  </motion.div>
               </motion.section>
            </main>
            {/* Trust Indicators Section */}
            <motion.section
               initial={{ opacity: 0, y: 50 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ delay: 0.9, duration: 0.8 }}
               className="relative z-10 bg-white/5 backdrop-blur-sm border-t border-white/10"
               aria-label="Trust indicators and security certifications"
            >
               <div className="container mx-auto px-4 py-16">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                     {trustIndicators.map((indicator, index) => {
                        const Icon = indicator.icon;
                        return (
                           <motion.div
                              key={indicator.title}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{
                                 delay: 1.1 + index * 0.1,
                                 duration: 0.6,
                              }}
                              className="text-center text-white"
                           >
                              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                 <Icon className="h-6 w-6 text-white" />
                              </div>
                              <h3 className="font-semibold mb-1 text-sm md:text-base">
                                 {indicator.title}
                              </h3>
                              <p className="text-white/70 text-xs md:text-sm">
                                 {indicator.description}
                              </p>
                           </motion.div>
                        );
                     })}
                  </div>
               </div>
            </motion.section>
         </div>

         {/* Services Overview Section */}
         <section
            className="relative z-10 bg-background/50 py-20"
            aria-labelledby="services-title"
         >
            <div className="container mx-auto px-4">
               <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                  className="text-center mb-16"
               >
                  <h2
                     id="services-title"
                     className="homepage-section-title mb-6 text-foreground"
                  >
                     Complete Banking Solutions
                  </h2>
                  <p className="homepage-section-subtitle text-muted-foreground max-w-3xl mx-auto">
                     From everyday banking to advanced investment tools, we
                     provide everything you need to manage and grow your wealth.
                  </p>
               </motion.div>

               <div className="homepage-grid-services">
                  {services.map((service, index) => {
                     const Icon = service.icon;
                     return (
                        <motion.div
                           key={service.title}
                           initial={{ opacity: 0, y: 30 }}
                           whileInView={{ opacity: 1, y: 0 }}
                           transition={{ delay: index * 0.1, duration: 0.6 }}
                           viewport={{ once: true }}
                        >
                           <Card className="h-full border-border bg-card/50 backdrop-blur-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                              <CardHeader>
                                 <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                                    <Icon className="h-6 w-6 text-primary" />
                                 </div>
                                 <CardTitle className="text-xl mb-2">
                                    {service.title}
                                 </CardTitle>
                              </CardHeader>
                              <CardContent>
                                 <p className="text-muted-foreground mb-4">
                                    {service.description}
                                 </p>
                                 <ul className="space-y-2">
                                    {service.features.map(
                                       (feature, featureIndex) => (
                                          <li
                                             key={featureIndex}
                                             className="flex items-center text-sm"
                                          >
                                             <CheckCircle className="h-4 w-4 text-success mr-2 flex-shrink-0" />
                                             {feature}
                                          </li>
                                       )
                                    )}
                                 </ul>
                              </CardContent>
                           </Card>
                        </motion.div>
                     );
                  })}
               </div>
            </div>
         </section>

         <div className="relative">
            <div className="absolute inset-0 -z-10" aria-hidden="true">
               <Image
                  src={bankingHero}
                  fill
                  className="object-cover"
                  alt=""
                  priority
                  role="presentation"
               />
               <div className="absolute inset-0 bg-gradient-to-br from-primary/40 via-primary/30 to-accent/20"></div>
            </div>

            {/* Benefits Section */}
            <section className="relative z-10 bg-white/5 backdrop-blur-sm border-t border-white/10 py-20">
               <div className="container mx-auto px-4">
                  <motion.div
                     initial={{ opacity: 0, y: 30 }}
                     whileInView={{ opacity: 1, y: 0 }}
                     transition={{ duration: 0.8 }}
                     viewport={{ once: true }}
                     className="text-center mb-16"
                  >
                     <h2 className="homepage-section-title mb-6 text-white">
                        Why Choose Paramount Bank?
                     </h2>
                     <p className="homepage-section-subtitle text-white/70 max-w-3xl mx-auto">
                        We&apos;re committed to providing exceptional banking
                        experiences with transparent pricing, premium support,
                        and innovative features.
                     </p>
                  </motion.div>
                  <div className="homepage-grid-benefits">
                     {benefits.map((benefit, index) => {
                        const Icon = benefit.icon;
                        return (
                           <motion.div
                              key={benefit.title}
                              initial={{ opacity: 0, y: 30 }}
                              whileInView={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.1, duration: 0.6 }}
                              viewport={{ once: true }}
                              className="text-center"
                           >
                              {/* <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6"> */}
                              <div className="w-16 h-16 bg-gradient-to-br from-primary to-primary-dark rounded-full flex items-center justify-center mx-auto mb-6">
                                 <Icon className="h-8 w-8 text-white" />
                              </div>
                              <h3 className="text-xl font-semibold mb-4 text-white">
                                 {benefit.title}
                              </h3>
                              <p className="text-white/70">
                                 {benefit.description}
                              </p>
                           </motion.div>
                        );
                     })}
                  </div>
               </div>
            </section>
            {/* Security & Compliance Section */}
            <section className="relative z-10 bg-primary/5 py-20">
               <div className="container mx-auto px-4">
                  <motion.div
                     initial={{ opacity: 0, y: 30 }}
                     whileInView={{ opacity: 1, y: 0 }}
                     transition={{ duration: 0.8 }}
                     viewport={{ once: true }}
                     className="text-center mb-16"
                  >
                     <h2 className="homepage-section-title mb-6 text-white">
                        Your Security is Our Priority
                     </h2>
                     <p className="homepage-section-subtitle text-white/70 max-w-3xl mx-auto">
                        We employ the highest standards of security and
                        compliance to protect your financial information and
                        transactions.
                     </p>
                  </motion.div>
                  <div className="homepage-grid-security">
                     <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true }}
                     >
                        <Card className="text-center p-8 border-border/10 bg-black/20 backdrop-blur-sm">
                           <Shield className="h-12 w-12 text-white/70 mx-auto mb-4" />
                           <h3 className="text-xl font-semibold mb-4 text-white/70">
                              Advanced Encryption
                           </h3>
                           <p className="text-white/50">
                              All data is protected with 256-bit SSL encryption
                              and stored in secure, geographically distributed
                              data centers.
                           </p>
                        </Card>
                     </motion.div>
                     <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1, duration: 0.6 }}
                        viewport={{ once: true }}
                     >
                        <Card className="text-center p-8 border-border/10 bg-black/20 backdrop-blur-sm">
                           <Award className="h-12 w-12 text-white/70 mx-auto mb-4" />
                           <h3 className="text-xl font-semibold mb-4 text-white/70">
                              Regulatory Compliance
                           </h3>
                           <p className="text-white/50">
                              Fully compliant with FDIC, PCI DSS, and SOX.
                              Regular audits ensure ongoing compliance.
                              compliance.
                           </p>
                        </Card>
                     </motion.div>
                     <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2, duration: 0.6 }}
                        viewport={{ once: true }}
                     >
                        <Card className="text-center p-8 border-border/10 bg-black/20 backdrop-blur-sm">
                           <Lock className="h-12 w-12 text-white/70 mx-auto mb-4" />
                           <h3 className="text-xl font-semibold mb-4 text-white/70">
                              Fraud Protection
                           </h3>
                           <p className="text-white/50">
                              Real-time fraud monitoring and instant alerts
                              protect your account from unauthorized access and
                              transactions.
                           </p>
                        </Card>
                     </motion.div>
                  </div>
               </div>
            </section>
         </div>

         {/* Final CTA Section */}
         <section className="relative z-10 bg-gradient-to-br from-primary to-primary-dark py-20">
            <div className="container mx-auto px-4 text-center">
               <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                  className="text-white"
               >
                  <h2 className="homepage-section-title mb-6 text-white">
                     Ready to Transform Your Banking?
                  </h2>
                  <p className="homepage-section-subtitle text-white/90 mb-8 sm:mb-12 max-w-3xl mx-auto px-4 sm:px-0">
                     Join over 500,000 customers who trust Paramount Bank for
                     their financial needs. Open your account in minutes and
                     experience the future of banking today.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center items-center px-4 sm:px-0">
                     <Button
                        asChild
                        size="xl"
                        className="homepage-button-touch text-white hover:bg-white/90 shadow-2xl w-full sm:w-auto"
                     >
                        <Link href="/signup">
                           Open Your Account Now
                           <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                     </Button>
                     <Button
                        asChild
                        variant="outline"
                        size="xl"
                        className="homepage-button-touch border border-white/30 bg-white/10 backdrop-blur-sm text-white hover:bg-white hover:text-primary w-full sm:w-auto"
                     >
                        <Link href="/login">Sign In to Your Account</Link>
                     </Button>
                  </div>
                  <p className="text-white/70 text-sm mt-8">
                     No minimum balance • No monthly fees • FDIC insured up to
                     $250,000
                  </p>
               </motion.div>
            </div>
         </section>

         {/* Footer */}
         <footer className="relative z-10 bg-background border-t border-border py-16">
            <div className="container mx-auto px-4">
               <div className="homepage-footer-grid mb-12">
                  <div className="col-span-1 md:col-span-2">
                     <div className="flex items-center space-x-2 mb-6">
                        <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                           <Building2 className="h-6 w-6 text-white" />
                        </div>
                        <span className="text-2xl font-bold text-foreground">
                           Paramount Bank
                        </span>
                     </div>
                     <p className="text-muted-foreground mb-6 max-w-md">
                        Empowering your financial future with innovative digital
                        banking solutions, exceptional security, and
                        personalized service.
                     </p>
                     <div className="flex space-x-4">
                        <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                           <Phone className="h-5 w-5 text-muted-foreground" />
                        </div>
                        <div>
                           <p className="font-semibold text-foreground">
                              24/7 Customer Support
                           </p>
                           <p className="text-muted-foreground">
                              1-800-PARAMOUNT
                           </p>
                        </div>
                     </div>
                  </div>

                  <div>
                     <h3 className="font-semibold text-foreground mb-4">
                        Banking
                     </h3>
                     <ul className="space-y-2 text-muted-foreground">
                        <li>
                           <Link
                              href="/signup"
                              className="hover:text-primary transition-colors"
                           >
                              Open Account
                           </Link>
                        </li>
                        <li>
                           <Link
                              href="/login"
                              className="hover:text-primary transition-colors"
                           >
                              Sign In
                           </Link>
                        </li>
                        <li>
                           <span className="cursor-not-allowed opacity-50">
                              Mobile App
                           </span>
                        </li>
                        <li>
                           <span className="cursor-not-allowed opacity-50">
                              ATM Locations
                           </span>
                        </li>
                     </ul>
                  </div>

                  <div>
                     <h3 className="font-semibold text-foreground mb-4">
                        Support
                     </h3>
                     <ul className="space-y-2 text-muted-foreground">
                        <li>
                           <span className="cursor-not-allowed opacity-50">
                              Help Center
                           </span>
                        </li>
                        <li>
                           <span className="cursor-not-allowed opacity-50">
                              Contact Us
                           </span>
                        </li>
                        <li>
                           <span className="cursor-not-allowed opacity-50">
                              Security Center
                           </span>
                        </li>
                        <li>
                           <span className="cursor-not-allowed opacity-50">
                              Privacy Policy
                           </span>
                        </li>
                     </ul>
                  </div>
               </div>

               <div className="border-t border-border pt-8">
                  <div className="flex flex-col md:flex-row justify-between items-center">
                     <p className="text-muted-foreground text-sm mb-4 md:mb-0">
                        © 2024 Paramount Bank. All rights reserved. Member FDIC.
                     </p>
                     <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                        <span>Equal Housing Lender</span>
                        <span>FDIC Insured</span>
                        <span>ISO 27001 Certified</span>
                     </div>
                  </div>
               </div>
            </div>
         </footer>
      </div>
   );
}
