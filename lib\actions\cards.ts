"use server";

import { revalidatePath } from "next/cache";
import { CardDetails, CreateCardData, CardApplication } from "@/lib/models";
import { createCard, getCardsByUserId, getCardById, updateCardStatus, updateCardLimits } from "@/lib/services/card";
import { getUserAccount } from "./accounts";

/**
 * Server Action: Apply for a new card
 */
export async function applyForCard(
  userId: string, 
  application: CardApplication
): Promise<{ success: boolean; card?: CardDetails; message?: string }> {
  try {
    // Get user's account
    const accountResult = await getUserAccount(userId);
    if (!accountResult.success || !accountResult.account) {
      return { success: false, message: "User account not found" };
    }

    const cardData: CreateCardData = {
      userId,
      accountId: accountResult.account.id,
      cardType: application.cardType,
      cardTier: application.cardTier,
      dailyLimit: application.dailyLimit ? parseFloat(application.dailyLimit) : undefined,
      currency: application.currency,
      billingAddress: application.billingAddress ? {
        street: application.billingAddress,
        city: application.billingCity || "",
        state: application.billingState || "",
        zipCode: application.billingZip || "",
        country: "US", // Default for demo
      } : undefined,
    };

    const result = await createCard(cardData);
    
    if (result.success) {
      // Revalidate card data
      revalidatePath("/dashboard");
      revalidatePath("/cards");
    }
    
    return result;
  } catch (error) {
    console.error("Apply for card action error:", error);
    return { success: false, message: "Internal server error" };
  }
}

/**
 * Server Action: Get user's cards
 */
export async function getUserCards(userId: string): Promise<{ success: boolean; cards?: CardDetails[]; message?: string }> {
  try {
    const result = await getCardsByUserId(userId);
    return result;
  } catch (error) {
    console.error("Get user cards action error:", error);
    return { success: false, message: "Internal server error" };
  }
}

/**
 * Server Action: Get card details
 */
export async function getCardDetails(cardId: string): Promise<{ success: boolean; card?: CardDetails; message?: string }> {
  try {
    const result = await getCardById(cardId);
    return result;
  } catch (error) {
    console.error("Get card details action error:", error);
    return { success: false, message: "Internal server error" };
  }
}

/**
 * Server Action: Block/Unblock card
 */
export async function toggleCardStatus(
  cardId: string, 
  action: "block" | "unblock"
): Promise<{ success: boolean; message?: string }> {
  try {
    const status = action === "block" ? "blocked" : "active";
    const result = await updateCardStatus(cardId, status);
    
    if (result.success) {
      // Revalidate card data
      revalidatePath("/dashboard");
      revalidatePath("/cards");
    }
    
    return result;
  } catch (error) {
    console.error("Toggle card status action error:", error);
    return { success: false, message: "Internal server error" };
  }
}

/**
 * Server Action: Update card spending limits
 */
export async function updateCardSpendingLimits(
  cardId: string, 
  dailyLimit: number
): Promise<{ success: boolean; message?: string }> {
  try {
    const result = await updateCardLimits(cardId, dailyLimit);
    
    if (result.success) {
      // Revalidate card data
      revalidatePath("/dashboard");
      revalidatePath("/cards");
    }
    
    return result;
  } catch (error) {
    console.error("Update card limits action error:", error);
    return { success: false, message: "Internal server error" };
  }
}
