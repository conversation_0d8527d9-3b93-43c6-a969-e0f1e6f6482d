"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DashboardBreadcrumb } from "@/components/ui/dashboard-breadcrumb";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { useAuth, useUserAccount } from "@/hooks";
import { motion } from "framer-motion";
import { Building, Copy, CreditCard, Smartphone, Wallet } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function Deposit() {
   const { user } = useAuth();
   const { data: userAccount } = useUserAccount(user?.id);
   const [showUnavailableDialog, setShowUnavailableDialog] = useState(false);
   const [unavailableMethod, setUnavailableMethod] = useState("");
   const [showBankDetailsDialog, setShowBankDetailsDialog] = useState(false);

   const depositMethods = [
      {
         id: "card",
         title: "Debit/Credit Card",
         description: "Instant deposit using your card",
         icon: CreditCard,
         fee: "Free",
         time: "Instant",
      },
      {
         id: "bank",
         title: "Bank Transfer",
         description: "Transfer from another bank account",
         icon: Building,
         fee: "Free",
         time: "1-3 business days",
      },
      {
         id: "mobile",
         title: "Mobile Deposit",
         description: "Deposit checks using your phone",
         icon: Smartphone,
         fee: "Free",
         time: "1-2 business days",
      },
   ];

   const handleMethodSelect = (methodId: string) => {
      if (methodId === "card" || methodId === "mobile") {
         // Show unavailable dialog for card and mobile methods
         setUnavailableMethod(
            depositMethods.find((m) => m.id === methodId)?.title || ""
         );
         setShowUnavailableDialog(true);
      } else if (methodId === "bank") {
         // Show bank details for bank transfer
         setShowBankDetailsDialog(true);
      }
   };

   const copyToClipboard = (text: string, label: string) => {
      navigator.clipboard.writeText(text);
      toast.success(`${label} copied to clipboard`);
   };

   return (
      <div>
         {/* Breadcrumbs */}
         <DashboardBreadcrumb items={[{ label: "Deposit" }]} />

         {/* Main Content */}
         <div className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               className="max-w-2xl mx-auto"
            >
               <div className="text-center mb-8">
                  <div className="mx-auto w-16 h-16 bg-warning/10 rounded-full flex items-center justify-center mb-4">
                     <Wallet className="w-8 h-8 text-warning" />
                  </div>
                  <h1 className="text-3xl font-bold text-foreground mb-2">
                     Add Funds
                  </h1>
                  <p className="text-muted-foreground">
                     Choose your preferred method to deposit money into your
                     account.
                  </p>
               </div>

               <div className="bank-card p-6">
                  <div className="space-y-6">
                     {/* Deposit Methods */}
                     <div>
                        <label className="text-sm font-medium text-foreground mb-4 block">
                           Choose Deposit Method
                        </label>
                        <div className="space-y-3">
                           {depositMethods.map((method) => {
                              const IconComponent = method.icon;
                              return (
                                 <Button
                                    key={method.id}
                                    variant="outline"
                                    className="w-full h-auto p-4 justify-start hover:bg-muted/30"
                                    onClick={() =>
                                       handleMethodSelect(method.id)
                                    }
                                 >
                                    <div className="flex items-center gap-4 w-full">
                                       <div className="p-2 bg-primary/10 rounded-lg">
                                          <IconComponent className="w-5 h-5 text-primary" />
                                       </div>
                                       <div className="flex-1 text-left">
                                          <h3 className="font-medium text-foreground">
                                             {method.title}
                                          </h3>
                                          <p className="text-sm text-muted-foreground">
                                             {method.description}
                                          </p>
                                          <div className="flex gap-4 mt-1">
                                             <span className="text-xs text-success">
                                                Fee: {method.fee}
                                             </span>
                                             <span className="text-xs text-muted-foreground">
                                                Time: {method.time}
                                             </span>
                                          </div>
                                       </div>
                                    </div>
                                 </Button>
                              );
                           })}
                        </div>
                     </div>

                     {/* Security Notice */}
                     <div className="mt-6 p-4 bg-muted/30 rounded-lg">
                        <p className="text-sm text-muted-foreground text-center">
                           🔒 All deposit methods are secured with bank-level
                           encryption
                        </p>
                     </div>
                  </div>
               </div>
            </motion.div>

            {/* Unavailable Method Dialog */}
            <Dialog
               open={showUnavailableDialog}
               onOpenChange={setShowUnavailableDialog}
            >
               <DialogContent>
                  <DialogHeader>
                     <DialogTitle>Service Temporarily Unavailable</DialogTitle>
                     <DialogDescription>
                        Depositing through {unavailableMethod} is unavailable at
                        the moment. Please try again later or contact support.
                     </DialogDescription>
                  </DialogHeader>
                  <div className="flex justify-end gap-2 mt-4">
                     <Button
                        variant="outline"
                        onClick={() => setShowUnavailableDialog(false)}
                     >
                        Close
                     </Button>
                     <Button
                        onClick={() => {
                           setShowUnavailableDialog(false);
                           // In a real app, this would open a support chat or redirect to support
                           toast.info("Contact <NAME_EMAIL>");
                        }}
                     >
                        Contact Support
                     </Button>
                  </div>
               </DialogContent>
            </Dialog>

            {/* Bank Details Dialog */}
            <Dialog
               open={showBankDetailsDialog}
               onOpenChange={setShowBankDetailsDialog}
            >
               <DialogContent className="max-w-md">
                  <DialogHeader>
                     <DialogTitle>Bank Transfer Details</DialogTitle>
                     <DialogDescription>
                        Use these details to transfer money from your external
                        bank account
                     </DialogDescription>
                  </DialogHeader>
                  <Card>
                     <CardHeader>
                        <CardTitle className="text-lg">
                           Paramount Bank
                        </CardTitle>
                     </CardHeader>
                     <CardContent className="space-y-4">
                        <div>
                           <Label className="text-sm font-medium">
                              Bank Name
                           </Label>
                           <div className="flex items-center justify-between mt-1">
                              <span className="text-sm">Paramount Bank</span>
                              <Button
                                 size="sm"
                                 variant="ghost"
                                 onClick={() =>
                                    copyToClipboard(
                                       "Paramount Bank",
                                       "Bank name"
                                    )
                                 }
                              >
                                 <Copy className="w-4 h-4" />
                              </Button>
                           </div>
                        </div>
                        <div>
                           <Label className="text-sm font-medium">
                              Routing Number
                           </Label>
                           <div className="flex items-center justify-between mt-1">
                              <span className="text-sm font-mono">
                                 *********
                              </span>
                              <Button
                                 size="sm"
                                 variant="ghost"
                                 onClick={() =>
                                    copyToClipboard(
                                       "*********",
                                       "Routing number"
                                    )
                                 }
                              >
                                 <Copy className="w-4 h-4" />
                              </Button>
                           </div>
                        </div>
                        <div>
                           <Label className="text-sm font-medium">
                              Account Number
                           </Label>
                           <div className="flex items-center justify-between mt-1">
                              <span className="text-sm font-mono">
                                 {userAccount?.accountNumber || "Loading..."}
                              </span>
                              <Button
                                 size="sm"
                                 variant="ghost"
                                 onClick={() =>
                                    copyToClipboard(
                                       userAccount?.accountNumber || "",
                                       "Account number"
                                    )
                                 }
                                 disabled={!userAccount?.accountNumber}
                              >
                                 <Copy className="w-4 h-4" />
                              </Button>
                           </div>
                        </div>
                        <div>
                           <Label className="text-sm font-medium">
                              Account Holder
                           </Label>
                           <div className="flex items-center justify-between mt-1">
                              <span className="text-sm">
                                 {user?.firstName} {user?.lastName}
                              </span>
                              <Button
                                 size="sm"
                                 variant="ghost"
                                 onClick={() =>
                                    copyToClipboard(
                                       `${user?.firstName} ${user?.lastName}`,
                                       "Account holder name"
                                    )
                                 }
                              >
                                 <Copy className="w-4 h-4" />
                              </Button>
                           </div>
                        </div>
                        <div className="p-3 bg-muted/50 rounded-lg">
                           <p className="text-xs text-muted-foreground">
                              <strong>Note:</strong> Transfers typically take
                              1-3 business days to process. Include your account
                              number in the transfer memo for faster processing.
                           </p>
                        </div>
                     </CardContent>
                  </Card>
                  <div className="flex justify-end mt-4">
                     <Button onClick={() => setShowBankDetailsDialog(false)}>
                        Done
                     </Button>
                  </div>
               </DialogContent>
            </Dialog>
         </div>
      </div>
   );
}
