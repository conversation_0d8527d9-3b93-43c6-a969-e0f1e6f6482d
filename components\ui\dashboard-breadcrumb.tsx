"use client";

import {
   B<PERSON><PERSON>rumb,
   BreadcrumbItem,
   BreadcrumbLink,
   BreadcrumbList,
   BreadcrumbPage,
   BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useRouter } from "next/navigation";

interface BreadcrumbItem {
   label: string;
   href?: string;
}

interface DashboardBreadcrumbProps {
   items: BreadcrumbItem[];
}

export function DashboardBreadcrumb({ items }: DashboardBreadcrumbProps) {
   const router = useRouter();

   return (
      <div className="container mx-auto px-4 py-4 !pb-0">
         <Breadcrumb>
            <BreadcrumbList>
               <BreadcrumbItem>
                  <BreadcrumbLink
                     onClick={() => router.push("/dashboard")}
                     className="cursor-pointer hover:text-primary"
                  >
                     Dashboard
                  </BreadcrumbLink>
               </BreadcrumbItem>

               {items.map((item, index) => (
                  <div key={index} className="flex items-center">
                     <BreadcrumbSeparator />
                     <BreadcrumbItem>
                        {item.href && index < items.length - 1 ? (
                           <BreadcrumbLink
                              onClick={() => router.push(item.href!)}
                              className="cursor-pointer hover:text-primary"
                           >
                              {item.label}
                           </BreadcrumbLink>
                        ) : (
                           <BreadcrumbPage>{item.label}</BreadcrumbPage>
                        )}
                     </BreadcrumbItem>
                  </div>
               ))}
            </BreadcrumbList>
         </Breadcrumb>
      </div>
   );
}
