import { Card, CARD_TIERS, CardDetails, CreateCardData } from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import {
   generateCardNumber,
   generateCVV,
   maskCardNumber,
} from "@/lib/utils/auth";
import { ObjectId, OptionalId } from "mongodb";

/**
 * Create a new card
 */
export async function createCard(
   data: CreateCardData
): Promise<{ success: boolean; card?: CardDetails; message?: string }> {
   try {
      const cardsCollection = await getCollection<Card>("cards");

      const tierInfo = CARD_TIERS[data.cardTier];
      const cardNumber = generateCardNumber();
      const cvv = generateCVV();

      // Set expiry date to 4 years from now
      const expiryDate = new Date();
      expiryDate.setFullYear(expiryDate.getFullYear() + 4);

      const newCard: OptionalId<Card> = {
         userId: new ObjectId(data.userId),
         accountId: new ObjectId(data.accountId),
         cardNumber: cardNumber, // In production, this should be encrypted
         cardType: data.cardType,
         cardTier: data.cardTier,
         cardName: tierInfo.name,
         expiryDate,
         cvv: cvv, // In production, this should be encrypted
         status: "active",
         dailyLimit: data.dailyLimit || 5000,
         monthlyLimit: (data.dailyLimit || 5000) * 30,
         currency: data.currency || "USD",
         issuanceFee: tierInfo.issuanceFee,
         cashbackRate: tierInfo.cashbackRate,
         features: [...tierInfo.features],
         billingAddress: data.billingAddress,
         createdAt: new Date(),
         updatedAt: new Date(),
      };

      const result = await cardsCollection.insertOne(newCard);

      if (result.insertedId) {
         const cardDetails: CardDetails = {
            id: result.insertedId.toString(),
            userId: data.userId,
            accountId: data.accountId,
            cardNumber: maskCardNumber(newCard.cardNumber),
            cardType: newCard.cardType,
            cardTier: newCard.cardTier,
            cardName: newCard.cardName,
            expiryDate: newCard.expiryDate,
            status: newCard.status,
            dailyLimit: newCard.dailyLimit,
            monthlyLimit: newCard.monthlyLimit,
            currency: newCard.currency,
            issuanceFee: newCard.issuanceFee,
            cashbackRate: newCard.cashbackRate,
            features: newCard.features,
            billingAddress: newCard.billingAddress,
         };

         return { success: true, card: cardDetails };
      }

      return { success: false, message: "Failed to create card" };
   } catch (error) {
      console.error("Error creating card:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Get cards by user ID
 */
export async function getCardsByUserId(
   userId: string
): Promise<{ success: boolean; cards?: CardDetails[]; message?: string }> {
   try {
      const cardsCollection = await getCollection<Card>("cards");
      const cards = await cardsCollection
         .find({ userId: new ObjectId(userId) })
         .toArray();

      const cardDetails: CardDetails[] = cards.map((card) => ({
         id: card._id!.toString(),
         userId: userId,
         accountId: card.accountId.toString(),
         cardNumber: maskCardNumber(card.cardNumber),
         cardType: card.cardType,
         cardTier: card.cardTier,
         cardName: card.cardName,
         expiryDate: card.expiryDate,
         status: card.status,
         dailyLimit: card.dailyLimit,
         monthlyLimit: card.monthlyLimit,
         currency: card.currency,
         issuanceFee: card.issuanceFee,
         cashbackRate: card.cashbackRate,
         features: card.features,
         billingAddress: card.billingAddress,
      }));

      return { success: true, cards: cardDetails };
   } catch (error) {
      console.error("Error getting cards:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Get card by ID
 */
export async function getCardById(
   cardId: string
): Promise<{ success: boolean; card?: CardDetails; message?: string }> {
   try {
      const cardsCollection = await getCollection<Card>("cards");
      const card = await cardsCollection.findOne({ _id: new ObjectId(cardId) });

      if (!card) {
         return { success: false, message: "Card not found" };
      }

      const cardDetails: CardDetails = {
         id: card._id!.toString(),
         userId: card.userId.toString(),
         accountId: card.accountId.toString(),
         cardNumber: maskCardNumber(card.cardNumber),
         cardType: card.cardType,
         cardTier: card.cardTier,
         cardName: card.cardName,
         expiryDate: card.expiryDate,
         status: card.status,
         dailyLimit: card.dailyLimit,
         monthlyLimit: card.monthlyLimit,
         currency: card.currency,
         issuanceFee: card.issuanceFee,
         cashbackRate: card.cashbackRate,
         features: card.features,
         billingAddress: card.billingAddress,
      };

      return { success: true, card: cardDetails };
   } catch (error) {
      console.error("Error getting card:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Update card status
 */
export async function updateCardStatus(
   cardId: string,
   status: "active" | "inactive" | "blocked" | "expired"
): Promise<{ success: boolean; message?: string }> {
   try {
      const cardsCollection = await getCollection<Card>("cards");

      const result = await cardsCollection.findOneAndUpdate(
         { _id: new ObjectId(cardId) },
         {
            $set: {
               status,
               updatedAt: new Date(),
            },
         },
         { returnDocument: "after" }
      );

      if (!result) {
         return { success: false, message: "Card not found" };
      }

      return { success: true };
   } catch (error) {
      console.error("Error updating card status:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Update card limits
 */
export async function updateCardLimits(
   cardId: string,
   dailyLimit: number
): Promise<{ success: boolean; message?: string }> {
   try {
      const cardsCollection = await getCollection<Card>("cards");

      const result = await cardsCollection.findOneAndUpdate(
         { _id: new ObjectId(cardId) },
         {
            $set: {
               dailyLimit,
               monthlyLimit: dailyLimit * 30,
               updatedAt: new Date(),
            },
         },
         { returnDocument: "after" }
      );

      if (!result) {
         return { success: false, message: "Card not found" };
      }

      return { success: true };
   } catch (error) {
      console.error("Error updating card limits:", error);
      return { success: false, message: "Internal server error" };
   }
}
