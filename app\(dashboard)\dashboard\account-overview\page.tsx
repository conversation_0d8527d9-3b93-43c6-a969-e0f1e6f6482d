"use client";

import { DashboardBreadcrumb } from "@/components/ui/dashboard-breadcrumb";
import { useAuth, useTransactionStatistics, useUserAccount } from "@/hooks";
import { motion } from "framer-motion";
import {
   BarChart3,
   CreditCard,
   DollarSign,
   Target,
   TrendingDown,
   TrendingUp,
} from "lucide-react";
import { useRouter } from "next/navigation";

export default function AccountOverview() {
   const router = useRouter();
   const { user } = useAuth();

   // Get user account data
   const {
      data: userAccount,
      isLoading: isLoadingAccount,
      error: accountError,
   } = useUserAccount(user?.id);

   // Get transaction statistics for current month
   const currentMonth = new Date();
   currentMonth.setDate(1);
   currentMonth.setHours(0, 0, 0, 0);

   const nextMonth = new Date(currentMonth);
   nextMonth.setMonth(nextMonth.getMonth() + 1);

   const {
      data: monthlyStats,
      isLoading: isLoadingStats,
      error: statsError,
   } = useTransactionStatistics(user?.id, currentMonth, nextMonth);

   // Calculate savings goal progress (example: 80% of income saved)
   const savingsGoal = 0.8;
   const monthlyIncome = monthlyStats?.totalIncome || 0;
   const monthlyExpenses = monthlyStats?.totalExpenses || 0;
   const actualSavings = monthlyIncome - monthlyExpenses;
   const savingsProgress =
      monthlyIncome > 0
         ? (actualSavings / (monthlyIncome * savingsGoal)) * 100
         : 0;

   const accountMetrics = [
      {
         title: "Total Balance",
         value: userAccount
            ? `$${userAccount.availableBalance.toFixed(2)}`
            : "$0.00",
         change: "+2.5%", // This would need historical data to calculate
         trend: "up",
         icon: DollarSign,
         color: "text-success",
      },
      {
         title: "Monthly Income",
         value: `$${monthlyIncome.toFixed(2)}`,
         change: "+8.2%", // This would need previous month data to calculate
         trend: "up",
         icon: TrendingUp,
         color: "text-success",
      },
      {
         title: "Monthly Spending",
         value: `$${monthlyExpenses.toFixed(2)}`,
         change: "-5.1%", // This would need previous month data to calculate
         trend: "down",
         icon: TrendingDown,
         color: "text-success",
      },
      {
         title: "Savings Goal",
         value: `${Math.min(savingsProgress, 100).toFixed(0)}%`,
         change: "+12%", // This would need previous month data to calculate
         trend: "up",
         icon: Target,
         color: "text-primary",
      },
   ];

   // Show loading state
   if (isLoadingAccount || isLoadingStats) {
      return (
         <div>
            <DashboardBreadcrumb items={[{ label: "Account Overview" }]} />
            <div className="container mx-auto px-4 py-8">
               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                  {[1, 2, 3, 4].map((i) => (
                     <div key={i} className="bg-card rounded-lg p-6 border">
                        <div className="animate-pulse">
                           <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                           <div className="h-8 bg-muted rounded w-1/2 mb-2"></div>
                           <div className="h-3 bg-muted rounded w-1/4"></div>
                        </div>
                     </div>
                  ))}
               </div>
            </div>
         </div>
      );
   }

   // Show error state
   if (accountError || statsError) {
      return (
         <div>
            <DashboardBreadcrumb items={[{ label: "Account Overview" }]} />
            <div className="container mx-auto px-4 py-8">
               <div className="text-center py-8">
                  <p className="text-destructive">
                     Failed to load account data. Please try again.
                  </p>
               </div>
            </div>
         </div>
      );
   }

   return (
      <div>
         {/* Breadcrumbs */}
         <DashboardBreadcrumb items={[{ label: "Account Overview" }]} />

         {/* Main Content */}
         <div className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
            >
               <div className="mb-8">
                  <h1 className="text-3xl font-bold text-foreground mb-2">
                     Account Overview
                  </h1>
                  <p className="text-muted-foreground">
                     Comprehensive view of your financial health and account
                     performance
                  </p>
               </div>

               {/* Account Metrics */}
               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                  {accountMetrics.map((metric, index) => {
                     const IconComponent = metric.icon;
                     return (
                        <motion.div
                           key={metric.title}
                           initial={{ opacity: 0, y: 20 }}
                           animate={{ opacity: 1, y: 0 }}
                           transition={{ duration: 0.6, delay: index * 0.1 }}
                           className="bank-card p-6"
                        >
                           <div className="flex items-center justify-between mb-4">
                              <div
                                 className={`p-3 rounded-lg ${metric.color} bg-current/10`}
                              >
                                 <IconComponent
                                    className={`w-6 h-6 ${metric.color}`}
                                 />
                              </div>
                              <div
                                 className={`text-sm font-medium ${metric.color}`}
                              >
                                 {metric.change}
                              </div>
                           </div>
                           <div>
                              <p className="text-sm text-muted-foreground mb-1">
                                 {metric.title}
                              </p>
                              <p className="text-2xl font-bold text-foreground">
                                 {metric.value}
                              </p>
                           </div>
                        </motion.div>
                     );
                  })}
               </div>

               {/* Additional Overview Content */}
               <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Account Summary */}
                  <motion.div
                     initial={{ opacity: 0, y: 20 }}
                     animate={{ opacity: 1, y: 0 }}
                     transition={{ duration: 0.6, delay: 0.4 }}
                     className="bank-card p-6"
                  >
                     <div className="flex items-center gap-3 mb-6">
                        <div className="p-2 bg-primary/10 rounded-lg">
                           <BarChart3 className="w-5 h-5 text-primary" />
                        </div>
                        <h2 className="text-xl font-semibold text-foreground">
                           Account Summary
                        </h2>
                     </div>

                     <div className="space-y-4">
                        <div className="flex justify-between items-center">
                           <span className="text-muted-foreground">
                              Account Type
                           </span>
                           <span className="font-medium text-foreground">
                              Premium Savings
                           </span>
                        </div>
                        <div className="flex justify-between items-center">
                           <span className="text-muted-foreground">
                              Account Number
                           </span>
                           <span className="font-medium text-foreground">
                              ****7890
                           </span>
                        </div>
                        <div className="flex justify-between items-center">
                           <span className="text-muted-foreground">
                              Interest Rate
                           </span>
                           <span className="font-medium text-success">
                              2.5% APY
                           </span>
                        </div>
                        <div className="flex justify-between items-center">
                           <span className="text-muted-foreground">
                              Account Status
                           </span>
                           <span className="font-medium text-success">
                              Active
                           </span>
                        </div>
                     </div>
                  </motion.div>

                  {/* Quick Actions */}
                  <motion.div
                     initial={{ opacity: 0, y: 20 }}
                     animate={{ opacity: 1, y: 0 }}
                     transition={{ duration: 0.6, delay: 0.5 }}
                     className="bank-card p-6"
                  >
                     <div className="flex items-center gap-3 mb-6">
                        <div className="p-2 bg-primary/10 rounded-lg">
                           <CreditCard className="w-5 h-5 text-primary" />
                        </div>
                        <h2 className="text-xl font-semibold text-foreground">
                           Quick Actions
                        </h2>
                     </div>

                     <div className="space-y-3">
                        <button
                           onClick={() =>
                              router.push("/dashboard/local-transfer")
                           }
                           className="w-full p-3 text-left rounded-lg border border-border hover:bg-muted/30 transition-colors"
                        >
                           <div className="font-medium text-foreground">
                              Send Money
                           </div>
                           <div className="text-sm text-muted-foreground">
                              Transfer funds locally or internationally
                           </div>
                        </button>
                        <button
                           onClick={() => router.push("/dashboard/deposit")}
                           className="w-full p-3 text-left rounded-lg border border-border hover:bg-muted/30 transition-colors"
                        >
                           <div className="font-medium text-foreground">
                              Deposit Funds
                           </div>
                           <div className="text-sm text-muted-foreground">
                              Add money to your account
                           </div>
                        </button>
                        <button
                           onClick={() => router.push("/dashboard/cards/apply")}
                           className="w-full p-3 text-left rounded-lg border border-border hover:bg-muted/30 transition-colors"
                        >
                           <div className="font-medium text-foreground">
                              Apply for Card
                           </div>
                           <div className="text-sm text-muted-foreground">
                              Get a new virtual or physical card
                           </div>
                        </button>
                     </div>
                  </motion.div>
               </div>
            </motion.div>
         </div>
      </div>
   );
}
