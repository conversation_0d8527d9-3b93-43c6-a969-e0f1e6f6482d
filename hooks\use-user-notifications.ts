"use client";

import {
  createGlobalNotificationAction,
  createUserNotificationAction,
  deleteNotificationAction,
  getUnreadNotificationCountAction,
  getUserNotificationsAction,
  markAllNotificationsAsReadAction,
  markNotificationAsReadAction,
} from "@/lib/actions";
import {
  CreateUserNotificationData,
  NotificationFilters,
  UserNotificationDetails,
} from "@/lib/models";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// ============================================================================
// USER NOTIFICATION HOOKS
// ============================================================================

/**
 * Hook to get user notifications
 */
export function useUserNotifications(filters: NotificationFilters) {
  return useQuery({
    queryKey: ["user-notifications", filters],
    queryFn: () => getUserNotificationsAction(filters),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute for real-time updates
  });
}

/**
 * Hook to get unread notification count
 */
export function useUnreadNotificationCount(userId: string) {
  return useQuery({
    queryKey: ["unread-notification-count", userId],
    queryFn: () => getUnreadNotificationCountAction(userId),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
    enabled: !!userId,
  });
}

/**
 * Hook to create user notification
 */
export function useCreateUserNotification() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateUserNotificationData) =>
      createUserNotificationAction(data),
    onSuccess: (result, variables) => {
      if (result.success) {
        // Invalidate and refetch user notifications
        queryClient.invalidateQueries({
          queryKey: ["user-notifications"],
        });
        
        // Invalidate unread count for the specific user
        if (variables.userId) {
          queryClient.invalidateQueries({
            queryKey: ["unread-notification-count", variables.userId],
          });
        }
      }
    },
  });
}

/**
 * Hook to create global notification
 */
export function useCreateGlobalNotification() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (
      data: Omit<CreateUserNotificationData, "userId" | "isGlobal"> & {
        createdBy: string;
      }
    ) => createGlobalNotificationAction(data),
    onSuccess: (result) => {
      if (result.success) {
        // Invalidate all user notifications since this affects all users
        queryClient.invalidateQueries({
          queryKey: ["user-notifications"],
        });
        queryClient.invalidateQueries({
          queryKey: ["unread-notification-count"],
        });
      }
    },
  });
}

/**
 * Hook to mark notification as read
 */
export function useMarkNotificationAsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (notificationId: string) =>
      markNotificationAsReadAction(notificationId),
    onSuccess: (result) => {
      if (result.success) {
        // Invalidate and refetch notifications
        queryClient.invalidateQueries({
          queryKey: ["user-notifications"],
        });
        queryClient.invalidateQueries({
          queryKey: ["unread-notification-count"],
        });
      }
    },
  });
}

/**
 * Hook to mark all notifications as read
 */
export function useMarkAllNotificationsAsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userId: string) => markAllNotificationsAsReadAction(userId),
    onSuccess: (result) => {
      if (result.success) {
        // Invalidate and refetch notifications
        queryClient.invalidateQueries({
          queryKey: ["user-notifications"],
        });
        queryClient.invalidateQueries({
          queryKey: ["unread-notification-count"],
        });
      }
    },
  });
}

/**
 * Hook to delete notification
 */
export function useDeleteNotification() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (notificationId: string) =>
      deleteNotificationAction(notificationId),
    onSuccess: (result) => {
      if (result.success) {
        // Invalidate and refetch notifications
        queryClient.invalidateQueries({
          queryKey: ["user-notifications"],
        });
        queryClient.invalidateQueries({
          queryKey: ["unread-notification-count"],
        });
      }
    },
  });
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Convert UserNotificationDetails to Notification interface for UI components
 */
export function convertToNotificationInterface(
  userNotifications: UserNotificationDetails[]
): Array<{
  id: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  timestamp: Date;
  read: boolean;
}> {
  return userNotifications.map((notification) => ({
    id: notification.id,
    title: notification.title,
    message: notification.message,
    type: notification.type,
    timestamp: notification.createdAt,
    read: notification.isRead,
  }));
}

/**
 * Helper function to create transaction notification
 */
export function createTransactionNotification(
  userId: string,
  type: "success" | "failed" | "pending",
  amount: number,
  recipientName?: string,
  transactionId?: string
): CreateUserNotificationData {
  const baseData = {
    userId,
    category: "transaction" as const,
    metadata: {
      amount,
      recipientName,
      transactionId,
    },
  };

  switch (type) {
    case "success":
      return {
        ...baseData,
        title: "Transfer Successful",
        message: recipientName
          ? `Your transfer of $${amount.toFixed(2)} to ${recipientName} was successful.`
          : `Your transaction of $${amount.toFixed(2)} was successful.`,
        type: "success",
      };
    case "failed":
      return {
        ...baseData,
        title: "Transfer Failed",
        message: `Your transaction of $${amount.toFixed(2)} failed. Please try again.`,
        type: "error",
      };
    case "pending":
      return {
        ...baseData,
        title: "Transaction Pending",
        message: `Your transaction of $${amount.toFixed(2)} is pending approval.`,
        type: "info",
      };
    default:
      return {
        ...baseData,
        title: "Transaction Update",
        message: `Transaction of $${amount.toFixed(2)} has been updated.`,
        type: "info",
      };
  }
}

/**
 * Helper function to create security notification
 */
export function createSecurityNotification(
  userId: string,
  device: string,
  location: string
): CreateUserNotificationData {
  return {
    userId,
    title: "Security Alert",
    message: `New device login detected from ${device} in ${location}.`,
    type: "warning",
    category: "security",
    metadata: {
      device,
      location,
    },
  };
}
