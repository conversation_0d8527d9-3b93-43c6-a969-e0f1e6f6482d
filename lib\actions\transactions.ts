"use server";

import {
   CreateTransactionData,
   TransactionDetails,
   TransactionFilters,
} from "@/lib/models";
import {
   createTransaction,
   getRecentTransactions,
   getTransactionById,
   getTransactions,
   getTransactionStats,
} from "@/lib/services/transaction";
import { revalidatePath } from "next/cache";

/**
 * Server Action: Create a new transaction
 */
export async function createNewTransaction(
   data: CreateTransactionData
): Promise<{
   success: boolean;
   transaction?: TransactionDetails;
   message?: string;
}> {
   try {
      const result = await createTransaction(data);

      if (result.success) {
         // Revalidate transaction and account data
         revalidatePath("/dashboard");
         revalidatePath("/account-overview");
         revalidatePath("/account-history");
      }

      return result;
   } catch (error) {
      console.error("Create transaction action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Get transactions with filters
 */
export async function getTransactionHistory(
   filters: TransactionFilters
): Promise<{
   success: boolean;
   transactions?: TransactionDetails[];
   message?: string;
}> {
   try {
      const result = await getTransactions(filters);
      return result;
   } catch (error) {
      console.error("Get transactions action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Get recent transactions
 */
export async function getRecentTransactionHistory(
   userId: string,
   limit?: number
): Promise<{
   success: boolean;
   transactions?: TransactionDetails[];
   message?: string;
}> {
   try {
      const result = await getRecentTransactions(userId, limit);
      return result;
   } catch (error) {
      console.error("Get recent transactions action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Get transaction by ID
 */
export async function getTransaction(transactionId: string): Promise<{
   success: boolean;
   transaction?: TransactionDetails;
   message?: string;
}> {
   try {
      const result = await getTransactionById(transactionId);
      return result;
   } catch (error) {
      console.error("Get transaction action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Get transaction statistics
 */
export async function getTransactionStatistics(
   userId: string,
   dateFrom?: Date,
   dateTo?: Date
): Promise<{
   success: boolean;
   stats?: {
      totalIncome: number;
      totalExpenses: number;
      transactionCount: number;
      categories: { [key: string]: number };
   };
   message?: string;
}> {
   try {
      const result = await getTransactionStats(userId, dateFrom, dateTo);
      return result;
   } catch (error) {
      console.error("Get transaction stats action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Send money (create debit transaction)
 */
export async function sendMoney(data: {
   userId: string;
   accountId: string;
   amount: number;
   recipientAccount: string;
   recipientName: string;
   description: string;
   transferType: "internal" | "external" | "international";
}): Promise<{
   success: boolean;
   transaction?: TransactionDetails;
   message?: string;
}> {
   try {
      const transactionData: CreateTransactionData = {
         userId: data.userId,
         accountId: data.accountId,
         type: "debit",
         amount: data.amount,
         description: data.description,
         category: "Transfer",
         status: "pending", // Default to pending for user-initiated transactions
         metadata: {
            transferType: data.transferType,
            recipientAccount: data.recipientAccount,
            recipientName: data.recipientName,
         },
      };

      const result = await createTransaction(transactionData);

      if (result.success) {
         // Revalidate transaction and account data
         revalidatePath("/dashboard");
         revalidatePath("/account-overview");
         revalidatePath("/account-history");
      }

      return result;
   } catch (error) {
      console.error("Send money action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Receive money (create credit transaction)
 */
export async function receiveMoney(data: {
   userId: string;
   accountId: string;
   amount: number;
   senderName: string;
   description: string;
}): Promise<{
   success: boolean;
   transaction?: TransactionDetails;
   message?: string;
}> {
   try {
      const transactionData: CreateTransactionData = {
         userId: data.userId,
         accountId: data.accountId,
         type: "credit",
         amount: data.amount,
         description: data.description,
         category: "Income",
         status: "pending", // Default to pending
         merchant: data.senderName,
      };

      const result = await createTransaction(transactionData);

      if (result.success) {
         // Revalidate transaction and account data
         revalidatePath("/dashboard");
         revalidatePath("/account-overview");
         revalidatePath("/account-history");
      }

      return result;
   } catch (error) {
      console.error("Receive money action error:", error);
      return { success: false, message: "Internal server error" };
   }
}
