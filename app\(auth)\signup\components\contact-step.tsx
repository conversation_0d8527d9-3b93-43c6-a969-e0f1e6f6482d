"use client";

import { Input } from "@/components/ui/input";
import { motion } from "framer-motion";
import { Mail, Phone } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { CountrySelect } from "./country-select";

// Step 2 Schema
export const contactSchema = z.object({
   email: z.string().email("Please enter a valid email address"),
   phoneNumber: z.string().min(10, "Please enter a valid phone number"),
   country: z.string().min(1, "Please select a country"),
});

export type ContactForm = z.infer<typeof contactSchema>;

interface ContactStepProps {
   form: UseFormReturn<ContactForm>;
}

export function ContactStep({ form }: ContactStepProps) {
   return (
      <motion.div
         key="step2"
         initial={{ opacity: 0, x: 20 }}
         animate={{ opacity: 1, x: 0 }}
         exit={{ opacity: 0, x: -20 }}
         className="space-y-6"
      >
         <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-foreground mb-2">
               Contact Details
            </h2>
            <p className="text-muted-foreground">
               How can we reach you?
            </p>
         </div>

         <Input
            label="Email Address"
            type="email"
            placeholder="Enter your email address"
            icon={<Mail size={18} />}
            error={form.formState.errors.email?.message}
            {...form.register("email")}
         />

         <Input
            label="Phone Number"
            type="tel"
            placeholder="Enter your phone number"
            icon={<Phone size={18} />}
            error={form.formState.errors.phoneNumber?.message}
            {...form.register("phoneNumber")}
         />

         <CountrySelect
            value={form.watch("country")}
            onValueChange={(value) => form.setValue("country", value)}
            error={form.formState.errors.country?.message}
         />
      </motion.div>
   );
}
