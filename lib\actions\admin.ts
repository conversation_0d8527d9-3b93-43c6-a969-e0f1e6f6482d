/* eslint-disable @typescript-eslint/no-explicit-any */
"use server";

import {
   AccountDetails,
   CreateNotificationMessageData,
   NotificationMessageDetails,
   TransactionDetails,
   UserProfile,
} from "@/lib/models";
import {
   adjustAccountBalance,
   createNotificationMessage,
   getAdminDashboardStats,
   getAllCards,
   getAllNotificationMessages,
   getAllTransactions,
   getAllUserAccounts,
   getAllUsers,
   getComprehensiveUserDetails,
   getPendingTransactionsCount,
   getUserAccountDetails,
   updateTransactionStatus,
   updateUserProfileByAdmin,
   updateUserRole,
   updateUserVerificationStatus,
} from "@/lib/services/admin";
import { revalidatePath } from "next/cache";

// ============================================================================
// DASHBOARD STATISTICS ACTIONS
// ============================================================================

/**
 * Server Action: Get admin dashboard statistics
 */
export async function getAdminDashboardStatsAction(): Promise<{
   success: boolean;
   stats?: {
      totalUsers: number;
      newUsersToday: number;
      totalTransactions: number;
      pendingTransactions: number;
      totalBalance: number;
      failedTransactions: number;
      completedTransactions: number;
      unverifiedUsers: number;
      totalAccounts: number;
      activeAccounts: number;
      totalCards: number;
      pendingCardApplications: number;
   };
   message?: string;
}> {
   try {
      return await getAdminDashboardStats();
   } catch (error) {
      console.error("Error in getAdminDashboardStatsAction:", error);
      return {
         success: false,
         message: "Failed to get dashboard statistics",
      };
   }
}

// ============================================================================
// USER MANAGEMENT ACTIONS
// ============================================================================

/**
 * Server Action: Get all users with pagination and filtering
 */
export async function getUsers(options: {
   page?: number;
   limit?: number;
   search?: string;
   role?: "user" | "admin";
   verificationStatus?: "verified" | "pending" | "unverified";
}): Promise<{
   success: boolean;
   users?: UserProfile[];
   total?: number;
   message?: string;
}> {
   try {
      const result = await getAllUsers(options);
      return result;
   } catch (error) {
      console.error("Get users action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Update user verification status
 */
export async function updateUserVerification(
   userId: string,
   verificationStatus: "verified" | "pending" | "unverified",
   adminId: string,
   reason?: string
): Promise<{ success: boolean; user?: UserProfile; message?: string }> {
   try {
      const result = await updateUserVerificationStatus(
         userId,
         verificationStatus,
         adminId,
         reason
      );

      if (result.success) {
         revalidatePath("/admin/users");
         revalidatePath("/admin/dashboard");
      }

      return result;
   } catch (error) {
      console.error("Update user verification action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Update user role
 */
export async function changeUserRole(
   userId: string,
   role: "user" | "admin",
   adminId: string,
   reason?: string
): Promise<{ success: boolean; user?: UserProfile; message?: string }> {
   try {
      const result = await updateUserRole(userId, role, adminId, reason);

      if (result.success) {
         revalidatePath("/admin/users");
         revalidatePath("/admin/dashboard");
      }

      return result;
   } catch (error) {
      console.error("Change user role action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

// ============================================================================
// ACCOUNT MANAGEMENT ACTIONS
// ============================================================================

/**
 * Server Action: Get user account details
 */
export async function getUserAccount(
   userId: string
): Promise<{ success: boolean; account?: AccountDetails; message?: string }> {
   try {
      const result = await getUserAccountDetails(userId);
      return result;
   } catch (error) {
      console.error("Get user account action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Get comprehensive user details for admin
 */
export async function getComprehensiveUserData(userId: string): Promise<{
   success: boolean;
   user?: UserProfile;
   account?: AccountDetails;
   cards?: any[];
   recentTransactions?: TransactionDetails[];
   message?: string;
}> {
   try {
      const result = await getComprehensiveUserDetails(userId);
      return result;
   } catch (error) {
      console.error("Get comprehensive user data action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Update user profile by admin
 */
export async function updateUserProfileByAdminAction(
   userId: string,
   updates: Partial<{
      firstName: string;
      lastName: string;
      email: string;
      phoneNumber: string;
      country: string;
      accountType: string;
      role: "user" | "admin";
      verificationStatus: "verified" | "pending" | "unverified";
   }>,
   adminId: string
): Promise<{ success: boolean; user?: UserProfile; message?: string }> {
   try {
      const result = await updateUserProfileByAdmin(userId, updates, adminId);

      if (result.success) {
         revalidatePath("/admin/users");
         revalidatePath(`/admin/users/${userId}`);
      }

      return result;
   } catch (error) {
      console.error("Update user profile by admin action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Get all user accounts
 */
export async function getAllUserAccountsAction(options: {
   page?: number;
   limit?: number;
   search?: string;
}): Promise<{
   success: boolean;
   accounts?: any[];
   total?: number;
   message?: string;
}> {
   try {
      const result = await getAllUserAccounts(options);
      return result;
   } catch (error) {
      console.error("Get all user accounts action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Adjust account balance
 */
export async function modifyAccountBalance(
   userId: string,
   amount: number,
   type: "credit" | "debit",
   adminId: string,
   reason?: string
): Promise<{ success: boolean; account?: AccountDetails; message?: string }> {
   try {
      const result = await adjustAccountBalance(
         userId,
         amount,
         type,
         adminId,
         reason
      );

      if (result.success) {
         revalidatePath("/admin/accounts");
         revalidatePath("/admin/dashboard");
         revalidatePath("/dashboard"); // User dashboard
      }

      return result;
   } catch (error) {
      console.error("Modify account balance action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

// ============================================================================
// TRANSACTION MANAGEMENT ACTIONS
// ============================================================================

/**
 * Server Action: Get all transactions with filtering
 */
export async function getTransactions(options: {
   page?: number;
   limit?: number;
   status?: "pending" | "completed" | "failed" | "cancelled";
   type?: "credit" | "debit";
   userId?: string;
   dateFrom?: Date;
   dateTo?: Date;
}): Promise<{
   success: boolean;
   transactions?: TransactionDetails[];
   total?: number;
   message?: string;
}> {
   try {
      const result = await getAllTransactions(options);
      return result;
   } catch (error) {
      console.error("Get transactions action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Update transaction status
 */
export async function changeTransactionStatus(
   transactionId: string,
   status: "pending" | "completed" | "failed" | "cancelled",
   adminId: string,
   reason?: string
): Promise<{
   success: boolean;
   transaction?: TransactionDetails;
   message?: string;
}> {
   try {
      const result = await updateTransactionStatus(
         transactionId,
         status,
         adminId,
         reason
      );

      if (result.success) {
         revalidatePath("/admin/transactions");
         revalidatePath("/admin/dashboard");
         revalidatePath("/dashboard"); // User dashboard
         revalidatePath("/dashboard/account-history");
      }

      return result;
   } catch (error) {
      console.error("Change transaction status action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Get pending transactions count
 */
export async function getPendingTransactionsCountAction(): Promise<{
   success: boolean;
   count?: number;
   message?: string;
}> {
   try {
      const result = await getPendingTransactionsCount();
      return result;
   } catch (error) {
      console.error("Get pending transactions count action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Get pending transactions with filtering
 */
export async function getPendingTransactions(options: {
   page?: number;
   limit?: number;
   type?: "credit" | "debit";
}): Promise<{
   success: boolean;
   transactions?: TransactionDetails[];
   total?: number;
   message?: string;
}> {
   try {
      const result = await getAllTransactions({
         ...options,
         status: "pending",
      });
      return result;
   } catch (error) {
      console.error("Get pending transactions action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

// ============================================================================
// NOTIFICATION MESSAGE MANAGEMENT ACTIONS
// ============================================================================

/**
 * Server Action: Get all notification messages
 */
export async function getNotificationMessages(): Promise<{
   success: boolean;
   messages?: NotificationMessageDetails[];
   message?: string;
}> {
   try {
      const result = await getAllNotificationMessages();
      return result;
   } catch (error) {
      console.error("Get notification messages action error:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Server Action: Create notification message
 */
export async function createNotificationMessageAction(
   data: CreateNotificationMessageData
): Promise<{
   success: boolean;
   message?: NotificationMessageDetails;
   error?: string;
}> {
   try {
      const result = await createNotificationMessage(data);

      if (result.success) {
         revalidatePath("/admin/notifications");
      }

      return result;
   } catch (error) {
      console.error("Create notification message action error:", error);
      return { success: false, error: "Internal server error" };
   }
}

// ============================================================================
// CARD MANAGEMENT ACTIONS
// ============================================================================

/**
 * Server Action: Get all cards for admin management
 */
export async function getCards(options: {
   page?: number;
   limit?: number;
   status?: "active" | "inactive" | "blocked" | "expired";
   cardTier?: "standard" | "gold" | "platinum" | "black";
   userId?: string;
}): Promise<{
   success: boolean;
   cards?: Array<{
      id: string;
      userId: string;
      userName: string;
      userEmail: string;
      cardNumber: string;
      cardTier: string;
      status: string;
      issuedDate: Date;
      expiryDate: Date;
      dailyLimit: number;
      monthlyLimit: number;
      currency: string;
   }>;
   total?: number;
   message?: string;
}> {
   try {
      const result = await getAllCards(options);
      return result;
   } catch (error) {
      console.error("Get cards action error:", error);
      return { success: false, message: "Internal server error" };
   }
}
