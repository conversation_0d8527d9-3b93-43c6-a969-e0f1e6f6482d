import { CreateUserD<PERSON>, User, UserProfile } from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import {
   hashPassword,
   hashPin,
   isValidEmail,
   isValidPassword,
   verifyPassword,
   verifyPin,
} from "@/lib/utils/auth";
import { ObjectId, OptionalId } from "mongodb";
import { createAccount } from "./account";

/**
 * Create a new user in the database
 */
export async function createUser(
   userData: CreateUserData
): Promise<{ success: boolean; user?: UserProfile; message?: string }> {
   try {
      console.log("Creating user with data:", {
         ...userData,
         password: "[REDACTED]",
         transactionPin: "[REDACTED]",
      });

      // Validate input data
      if (!isValidEmail(userData.email)) {
         console.log("Invalid email format:", userData.email);
         return { success: false, message: "Invalid email format" };
      }

      const passwordValidation = isValidPassword(userData.password);
      if (!passwordValidation.isValid) {
         return { success: false, message: passwordValidation.message };
      }

      if (!/^\d{4}$/.test(userData.transactionPin)) {
         return {
            success: false,
            message: "Transaction PIN must be exactly 4 digits",
         };
      }

      console.log("Getting users collection...");
      const usersCollection = await getCollection<User>("users");
      console.log("Users collection obtained successfully");

      // Check if user already exists
      console.log("Checking for existing user...");
      const existingUser = await usersCollection.findOne({
         $or: [{ email: userData.email }, { username: userData.username }],
      });
      console.log("Existing user check completed:", !!existingUser);

      if (existingUser) {
         return {
            success: false,
            message:
               existingUser.email === userData.email
                  ? "Email already exists"
                  : "Username already exists",
         };
      }

      // Hash password and PIN
      const hashedPassword = await hashPassword(userData.password);
      const hashedPin = await hashPin(userData.transactionPin);

      // Create user document
      const newUser: OptionalId<User> = {
         email: userData.email,
         password: hashedPassword,
         firstName: userData.firstName,
         lastName: userData.lastName,
         username: userData.username,
         phoneNumber: userData.phoneNumber,
         country: userData.country,
         accountType: userData.accountType,
         role: userData.role || "user",
         verificationStatus: "verified", // Auto-verify for demo
         transactionPin: hashedPin,
         createdAt: new Date(),
         updatedAt: new Date(),
      };

      const result = await usersCollection.insertOne(newUser);

      if (result.insertedId) {
         // Create a default account for the user
         const accountResult = await createAccount({
            userId: result.insertedId.toString(),
            accountType: userData.accountType,
            initialDeposit: 0,
            currency: "USD",
         });

         if (!accountResult.success) {
            // If account creation fails, we should ideally rollback the user creation
            // For now, we'll log the error and continue
            console.error(
               "Failed to create account for user:",
               accountResult.message
            );
         }

         const userProfile: UserProfile = {
            id: result.insertedId.toString(),
            email: newUser.email,
            firstName: newUser.firstName,
            lastName: newUser.lastName,
            username: newUser.username,
            phoneNumber: newUser.phoneNumber,
            country: newUser.country,
            accountType: newUser.accountType,
            verificationStatus: newUser.verificationStatus,
            role: newUser.role,
         };

         return { success: true, user: userProfile };
      }

      return { success: false, message: "Failed to create user" };
   } catch (error) {
      console.error("Error creating user:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Authenticate user login
 */
export async function authenticateUser(
   email: string,
   password: string
): Promise<{ success: boolean; user?: UserProfile; message?: string }> {
   try {
      if (!isValidEmail(email)) {
         return { success: false, message: "Invalid email format" };
      }

      const usersCollection = await getCollection<User>("users");
      const user = await usersCollection.findOne({ email });

      if (!user) {
         return { success: false, message: "Invalid credentials" };
      }

      const isPasswordValid = await verifyPassword(password, user.password);
      if (!isPasswordValid) {
         return { success: false, message: "Invalid credentials" };
      }

      const userProfile: UserProfile = {
         id: user._id!.toString(),
         email: user.email,
         firstName: user.firstName,
         lastName: user.lastName,
         username: user.username,
         phoneNumber: user.phoneNumber,
         country: user.country,
         accountType: user.accountType,
         role: user.role,
         verificationStatus: user.verificationStatus,
      };

      return { success: true, user: userProfile };
   } catch (error) {
      console.error("Error authenticating user:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Verify user's transaction PIN
 */
export async function verifyUserPin(
   userId: string,
   pin: string
): Promise<{ success: boolean; message?: string }> {
   try {
      if (!/^\d{4}$/.test(pin)) {
         return { success: false, message: "PIN must be exactly 4 digits" };
      }

      const usersCollection = await getCollection<User>("users");
      const user = await usersCollection.findOne({ _id: new ObjectId(userId) });

      if (!user) {
         return { success: false, message: "User not found" };
      }

      const isPinValid = await verifyPin(pin, user.transactionPin);
      if (!isPinValid) {
         return { success: false, message: "Invalid PIN" };
      }

      return { success: true };
   } catch (error) {
      console.error("Error verifying PIN:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Get user by ID
 */
export async function getUserById(
   userId: string
): Promise<{ success: boolean; user?: UserProfile; message?: string }> {
   try {
      const usersCollection = await getCollection<User>("users");
      const user = await usersCollection.findOne({ _id: new ObjectId(userId) });

      if (!user) {
         return { success: false, message: "User not found" };
      }

      const userProfile: UserProfile = {
         id: user._id!.toString(),
         email: user.email,
         firstName: user.firstName,
         lastName: user.lastName,
         username: user.username,
         phoneNumber: user.phoneNumber,
         country: user.country,
         accountType: user.accountType,
         role: user.role,
         verificationStatus: user.verificationStatus,
      };

      return { success: true, user: userProfile };
   } catch (error) {
      console.error("Error getting user:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Update user profile
 */
export async function updateUserProfile(
   userId: string,
   updates: Partial<
      Pick<User, "firstName" | "lastName" | "phoneNumber" | "country">
   >
): Promise<{ success: boolean; user?: UserProfile; message?: string }> {
   try {
      const usersCollection = await getCollection<User>("users");

      const updateData = {
         ...updates,
         updatedAt: new Date(),
      };

      const result = await usersCollection.findOneAndUpdate(
         { _id: new ObjectId(userId) },
         { $set: updateData },
         { returnDocument: "after" }
      );

      if (!result) {
         return { success: false, message: "User not found" };
      }

      const userProfile: UserProfile = {
         id: result._id!.toString(),
         email: result.email,
         firstName: result.firstName,
         lastName: result.lastName,
         username: result.username,
         phoneNumber: result.phoneNumber,
         country: result.country,
         accountType: result.accountType,
         role: result.role,
         verificationStatus: result.verificationStatus,
      };

      return { success: true, user: userProfile };
   } catch (error) {
      console.error("Error updating user:", error);
      return { success: false, message: "Internal server error" };
   }
}
