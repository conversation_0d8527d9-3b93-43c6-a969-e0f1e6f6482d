"use server";

import { revalidatePath } from "next/cache";
import { CreateUserData, AuthResponse, UserProfile } from "@/lib/models";
import { createUser, authenticateUser, verifyUserPin, getUserById, updateUserProfile } from "@/lib/services/user";

/**
 * Server Action: Register a new user
 */
export async function registerUser(userData: CreateUserData): Promise<AuthResponse> {
  try {
    const result = await createUser(userData);
    
    if (result.success && result.user) {
      // Revalidate any cached user data
      revalidatePath("/dashboard");
      return { success: true, user: result.user };
    }
    
    return { success: false, message: result.message || "Registration failed" };
  } catch (error) {
    console.error("Register user action error:", error);
    return { success: false, message: "Internal server error" };
  }
}

/**
 * Server Action: Login user
 */
export async function loginUser(email: string, password: string): Promise<AuthResponse> {
  try {
    const result = await authenticate<PERSON><PERSON>(email, password);
    
    if (result.success && result.user) {
      // Revalidate any cached user data
      revalidatePath("/dashboard");
      return { success: true, user: result.user };
    }
    
    return { success: false, message: result.message || "Login failed" };
  } catch (error) {
    console.error("Login user action error:", error);
    return { success: false, message: "Internal server error" };
  }
}

/**
 * Server Action: Verify user PIN
 */
export async function verifyPin(userId: string, pin: string): Promise<{ success: boolean; message?: string }> {
  try {
    const result = await verifyUserPin(userId, pin);
    return result;
  } catch (error) {
    console.error("Verify PIN action error:", error);
    return { success: false, message: "Internal server error" };
  }
}

/**
 * Server Action: Get user profile
 */
export async function getUserProfile(userId: string): Promise<{ success: boolean; user?: UserProfile; message?: string }> {
  try {
    const result = await getUserById(userId);
    return result;
  } catch (error) {
    console.error("Get user profile action error:", error);
    return { success: false, message: "Internal server error" };
  }
}

/**
 * Server Action: Update user profile
 */
export async function updateUser(
  userId: string,
  updates: { firstName?: string; lastName?: string; phoneNumber?: string; country?: string }
): Promise<{ success: boolean; user?: UserProfile; message?: string }> {
  try {
    const result = await updateUserProfile(userId, updates);
    
    if (result.success) {
      // Revalidate user data
      revalidatePath("/dashboard");
      revalidatePath("/profile");
    }
    
    return result;
  } catch (error) {
    console.error("Update user action error:", error);
    return { success: false, message: "Internal server error" };
  }
}
