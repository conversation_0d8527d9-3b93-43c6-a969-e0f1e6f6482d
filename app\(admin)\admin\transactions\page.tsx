/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { TransactionStatusBadge } from "@/components/ui/transaction-status-badge";
import { useAdminTransactions } from "@/hooks";
import { motion } from "framer-motion";
import {
   AlertTriangle,
   DollarSign,
   Download,
   Edit,
   Eye,
   Filter,
   Plus,
   Trash2,
   TrendingDown,
   TrendingUp,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function AdminTransactions() {
   const router = useRouter();
   const [searchTerm, setSearchTerm] = useState("");
   const [statusFilter, setStatusFilter] = useState<
      "all" | "pending" | "completed" | "failed" | "cancelled"
   >("all");
   const [typeFilter, setTypeFilter] = useState<"all" | "credit" | "debit">(
      "all"
   );
   const [userIdFilter, setUserIdFilter] = useState("");
   const [currentPage, setCurrentPage] = useState(1);
   const pageSize = 20;

   // Get transactions with filtering
   const {
      data: transactionsData,
      isLoading,
      error,
      refetch,
   } = useAdminTransactions({
      status: statusFilter === "all" ? undefined : statusFilter,
      type: typeFilter === "all" ? undefined : typeFilter,
      userId: userIdFilter || undefined,
      page: currentPage,
      limit: pageSize,
   });

   const transactions = transactionsData?.transactions || [];
   const totalTransactions = transactionsData?.total || 0;
   const totalPages = Math.ceil(totalTransactions / pageSize);

   const handleViewTransaction = (transactionId: string) => {
      router.push(`/admin/transactions/${transactionId}`);
   };

   const handleEditTransaction = (transactionId: string) => {
      router.push(`/admin/transactions/${transactionId}/edit`);
   };

   const handleDeleteTransaction = (transactionId: string) => {
      // In real app, this would show a confirmation dialog
      console.log("Delete transaction:", transactionId);
   };

   const handleAddTransaction = () => {
      router.push("/admin/transactions/new");
   };

   const handleExportTransactions = () => {
      // In real app, this would export transactions to CSV/Excel
      console.log("Export transactions");
   };

   const getTypeIcon = (type: string) => {
      return type === "credit" ? (
         <TrendingUp className="h-4 w-4 text-green-600" />
      ) : (
         <TrendingDown className="h-4 w-4 text-red-600" />
      );
   };

   const getTypeBadge = (type: string) => {
      return (
         <Badge
            variant={type === "credit" ? "default" : "destructive"}
            className={
               type === "credit"
                  ? "bg-green-100 text-green-800 border-green-200"
                  : "bg-red-100 text-red-800 border-red-200"
            }
         >
            {getTypeIcon(type)}
            <span className="ml-1 capitalize">{type}</span>
         </Badge>
      );
   };

   const stats = {
      total: totalTransactions,
      pending: transactions.filter((t) => t.status === "pending").length,
      completed: transactions.filter((t) => t.status === "completed").length,
      failed: transactions.filter((t) => t.status === "failed").length,
      cancelled: transactions.filter((t) => t.status === "cancelled").length,
      totalAmount: transactions.reduce(
         (sum, t) => sum + (t.type === "credit" ? t.amount : -t.amount),
         0
      ),
   };

   return (
      <div className="container mx-auto px-4 py-8">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
         >
            {/* Header */}
            <div className="mb-8">
               <div className="flex items-center justify-between">
                  <div>
                     <h1 className="text-3xl font-bold text-foreground mb-2">
                        Transaction Management
                     </h1>
                     <p className="text-muted-foreground">
                        View, edit, add, and delete transaction entries with
                        comprehensive filtering.
                     </p>
                  </div>
                  <div className="flex gap-2">
                     <Button
                        onClick={handleExportTransactions}
                        variant="outline"
                     >
                        <Download className="h-4 w-4 mr-2" />
                        Export
                     </Button>
                     <Button onClick={handleAddTransaction}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Transaction
                     </Button>
                  </div>
               </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <DollarSign className="h-5 w-5 text-muted-foreground" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Total
                           </p>
                           <p className="text-xl font-bold">{stats.total}</p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <div className="h-5 w-5 bg-yellow-100 rounded-full flex items-center justify-center">
                           <div className="h-2 w-2 bg-yellow-600 rounded-full"></div>
                        </div>
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Pending
                           </p>
                           <p className="text-xl font-bold text-yellow-600">
                              {stats.pending}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <div className="h-5 w-5 bg-green-100 rounded-full flex items-center justify-center">
                           <div className="h-2 w-2 bg-green-600 rounded-full"></div>
                        </div>
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Completed
                           </p>
                           <p className="text-xl font-bold text-green-600">
                              {stats.completed}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <div className="h-5 w-5 bg-red-100 rounded-full flex items-center justify-center">
                           <div className="h-2 w-2 bg-red-600 rounded-full"></div>
                        </div>
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Failed
                           </p>
                           <p className="text-xl font-bold text-red-600">
                              {stats.failed}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <div className="h-5 w-5 bg-gray-100 rounded-full flex items-center justify-center">
                           <div className="h-2 w-2 bg-gray-600 rounded-full"></div>
                        </div>
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Cancelled
                           </p>
                           <p className="text-xl font-bold text-gray-600">
                              {stats.cancelled}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <DollarSign className="h-5 w-5 text-blue-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Net Amount
                           </p>
                           <p
                              className={`text-xl font-bold ${
                                 stats.totalAmount >= 0
                                    ? "text-green-600"
                                    : "text-red-600"
                              }`}
                           >
                              $
                              {Math.abs(stats.totalAmount).toLocaleString(
                                 "en-US",
                                 { minimumFractionDigits: 2 }
                              )}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
            </div>

            {/* Filters */}
            <Card className="mb-6">
               <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                     <Filter className="h-5 w-5" />
                     Search & Filter
                  </CardTitle>
               </CardHeader>
               <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                     <div>
                        <Label htmlFor="search">Search Transactions</Label>
                        <Input
                           id="search"
                           placeholder="Search by description, reference..."
                           value={searchTerm}
                           onChange={(e) => setSearchTerm(e.target.value)}
                           className="mt-1"
                        />
                     </div>
                     <div>
                        <Label htmlFor="status-filter">Status</Label>
                        <Select
                           value={statusFilter}
                           onValueChange={(value: any) =>
                              setStatusFilter(value)
                           }
                        >
                           <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Filter by status" />
                           </SelectTrigger>
                           <SelectContent>
                              <SelectItem value="all">All Statuses</SelectItem>
                              <SelectItem value="pending">Pending</SelectItem>
                              <SelectItem value="completed">
                                 Completed
                              </SelectItem>
                              <SelectItem value="failed">Failed</SelectItem>
                              <SelectItem value="cancelled">
                                 Cancelled
                              </SelectItem>
                           </SelectContent>
                        </Select>
                     </div>
                     <div>
                        <Label htmlFor="type-filter">Type</Label>
                        <Select
                           value={typeFilter}
                           onValueChange={(value: any) => setTypeFilter(value)}
                        >
                           <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Filter by type" />
                           </SelectTrigger>
                           <SelectContent>
                              <SelectItem value="all">All Types</SelectItem>
                              <SelectItem value="credit">Credit</SelectItem>
                              <SelectItem value="debit">Debit</SelectItem>
                           </SelectContent>
                        </Select>
                     </div>
                     <div>
                        <Label htmlFor="user-filter">User ID</Label>
                        <Input
                           id="user-filter"
                           placeholder="Filter by user ID..."
                           value={userIdFilter}
                           onChange={(e) => setUserIdFilter(e.target.value)}
                           className="mt-1"
                        />
                     </div>
                  </div>
               </CardContent>
            </Card>

            {/* Transactions List */}
            <Card>
               <CardHeader>
                  <div className="flex items-center justify-between">
                     <CardTitle>Transactions ({totalTransactions})</CardTitle>
                     <div className="text-sm text-muted-foreground">
                        Page {currentPage} of {totalPages}
                     </div>
                  </div>
               </CardHeader>
               <CardContent>
                  {isLoading ? (
                     <div className="space-y-4">
                        {Array.from({ length: 5 }).map((_, index) => (
                           <div
                              key={index}
                              className="flex items-center gap-4 p-4 border rounded-lg"
                           >
                              <Skeleton className="h-10 w-10 rounded-full" />
                              <div className="flex-1 space-y-2">
                                 <Skeleton className="h-4 w-48" />
                                 <Skeleton className="h-3 w-32" />
                              </div>
                              <Skeleton className="h-6 w-20" />
                              <Skeleton className="h-6 w-16" />
                              <Skeleton className="h-8 w-24" />
                           </div>
                        ))}
                     </div>
                  ) : error ? (
                     <div className="text-center py-8">
                        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                        <p className="text-red-600 mb-4">
                           Failed to load transactions
                        </p>
                        <Button onClick={() => refetch()} variant="outline">
                           Try Again
                        </Button>
                     </div>
                  ) : transactions.length === 0 ? (
                     <div className="text-center py-8">
                        <DollarSign className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground">
                           No transactions found
                        </p>
                     </div>
                  ) : (
                     <>
                        <div className="space-y-4">
                           {transactions.map((transaction) => (
                              <div
                                 key={transaction.id}
                                 className="flex items-center gap-4 p-4 border rounded-lg hover:bg-muted/30 transition-colors"
                              >
                                 <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                                    {getTypeIcon(transaction.type)}
                                 </div>
                                 <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                       <p className="font-medium">
                                          {transaction.description}
                                       </p>
                                       <TransactionStatusBadge
                                          status={transaction.status}
                                       />
                                       {getTypeBadge(transaction.type)}
                                    </div>
                                    <p className="text-sm text-muted-foreground">
                                       ID: {transaction.transactionId} • User:{" "}
                                       {transaction.userId}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                       {transaction.date.toLocaleString()} •
                                       Category: {transaction.category}
                                       {transaction.merchant &&
                                          ` • Merchant: ${transaction.merchant}`}
                                    </p>
                                 </div>
                                 <div className="text-right">
                                    <p
                                       className={`font-semibold text-lg ${
                                          transaction.type === "credit"
                                             ? "text-green-600"
                                             : "text-red-600"
                                       }`}
                                    >
                                       {transaction.type === "credit"
                                          ? "+"
                                          : "-"}
                                       $
                                       {transaction.amount.toLocaleString(
                                          "en-US",
                                          { minimumFractionDigits: 2 }
                                       )}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                       {transaction.currency}
                                    </p>
                                 </div>
                                 <div className="flex gap-2">
                                    <Button
                                       size="sm"
                                       variant="outline"
                                       onClick={() =>
                                          handleViewTransaction(transaction.id)
                                       }
                                    >
                                       <Eye className="w-4 h-4" />
                                    </Button>
                                    <Button
                                       size="sm"
                                       variant="outline"
                                       onClick={() =>
                                          handleEditTransaction(transaction.id)
                                       }
                                    >
                                       <Edit className="w-4 h-4" />
                                    </Button>
                                    <Button
                                       size="sm"
                                       variant="outline"
                                       className="text-red-600 border-red-200 hover:bg-red-50"
                                       onClick={() =>
                                          handleDeleteTransaction(
                                             transaction.id
                                          )
                                       }
                                    >
                                       <Trash2 className="w-4 h-4" />
                                    </Button>
                                 </div>
                              </div>
                           ))}
                        </div>

                        {/* Pagination */}
                        {totalPages > 1 && (
                           <div className="flex items-center justify-between mt-6">
                              <Button
                                 variant="outline"
                                 onClick={() =>
                                    setCurrentPage((prev) =>
                                       Math.max(1, prev - 1)
                                    )
                                 }
                                 disabled={currentPage === 1}
                              >
                                 Previous
                              </Button>
                              <div className="flex items-center gap-2">
                                 {Array.from(
                                    { length: Math.min(5, totalPages) },
                                    (_, i) => {
                                       const page = i + 1;
                                       return (
                                          <Button
                                             key={page}
                                             variant={
                                                currentPage === page
                                                   ? "default"
                                                   : "outline"
                                             }
                                             size="sm"
                                             onClick={() =>
                                                setCurrentPage(page)
                                             }
                                          >
                                             {page}
                                          </Button>
                                       );
                                    }
                                 )}
                              </div>
                              <Button
                                 variant="outline"
                                 onClick={() =>
                                    setCurrentPage((prev) =>
                                       Math.min(totalPages, prev + 1)
                                    )
                                 }
                                 disabled={currentPage === totalPages}
                              >
                                 Next
                              </Button>
                           </div>
                        )}
                     </>
                  )}
               </CardContent>
            </Card>
         </motion.div>
      </div>
   );
}
