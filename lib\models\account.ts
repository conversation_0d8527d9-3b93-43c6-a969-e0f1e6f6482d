import { Document, ObjectId } from "mongodb";

export interface Account extends Document {
  _id?: ObjectId;
  userId: ObjectId;
  accountNumber: string;
  routingNumber: string;
  accountType: string;
  accountStatus: "Active" | "Inactive" | "Suspended" | "Closed";
  availableBalance: number;
  totalBalance: number;
  currency: string;
  dateOpened: Date;
  branch: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AccountDetails {
  id: string;
  userId: string;
  accountNumber: string;
  routingNumber: string;
  accountType: string;
  accountStatus: "Active" | "Inactive" | "Suspended" | "Closed";
  availableBalance: number;
  totalBalance: number;
  currency: string;
  dateOpened: Date;
  branch: string;
}

export interface CreateAccountData {
  userId: string;
  accountType: string;
  initialDeposit?: number;
  currency?: string;
  branch?: string;
}

export interface UpdateBalanceData {
  accountId: string;
  amount: number;
  type: "credit" | "debit";
  description: string;
}
