import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react";

interface TransactionStatusBadgeProps {
  status: "pending" | "completed" | "failed" | "cancelled";
  className?: string;
  showIcon?: boolean;
}

export function TransactionStatusBadge({ 
  status, 
  className,
  showIcon = true 
}: TransactionStatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case "pending":
        return {
          label: "Pending",
          variant: "secondary" as const,
          className: "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800",
          icon: Clock,
        };
      case "completed":
        return {
          label: "Completed",
          variant: "default" as const,
          className: "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",
          icon: CheckCircle,
        };
      case "failed":
        return {
          label: "Failed",
          variant: "destructive" as const,
          className: "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800",
          icon: XCircle,
        };
      case "cancelled":
        return {
          label: "Cancelled",
          variant: "outline" as const,
          className: "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800",
          icon: AlertCircle,
        };
      default:
        return {
          label: "Unknown",
          variant: "outline" as const,
          className: "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800",
          icon: AlertCircle,
        };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <Badge 
      variant={config.variant}
      className={cn(
        "inline-flex items-center gap-1 text-xs font-medium",
        config.className,
        className
      )}
    >
      {showIcon && <Icon className="h-3 w-3" />}
      {config.label}
    </Badge>
  );
}

// Utility function to get status color for other components
export function getTransactionStatusColor(status: "pending" | "completed" | "failed" | "cancelled") {
  switch (status) {
    case "pending":
      return "text-yellow-600 dark:text-yellow-400";
    case "completed":
      return "text-green-600 dark:text-green-400";
    case "failed":
      return "text-red-600 dark:text-red-400";
    case "cancelled":
      return "text-gray-600 dark:text-gray-400";
    default:
      return "text-gray-600 dark:text-gray-400";
  }
}

// Utility function to get status background color
export function getTransactionStatusBg(status: "pending" | "completed" | "failed" | "cancelled") {
  switch (status) {
    case "pending":
      return "bg-yellow-100 dark:bg-yellow-900/20";
    case "completed":
      return "bg-green-100 dark:bg-green-900/20";
    case "failed":
      return "bg-red-100 dark:bg-red-900/20";
    case "cancelled":
      return "bg-gray-100 dark:bg-gray-900/20";
    default:
      return "bg-gray-100 dark:bg-gray-900/20";
  }
}
