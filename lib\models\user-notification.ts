/* eslint-disable @typescript-eslint/no-explicit-any */
import { Document, ObjectId } from "mongodb";

export interface UserNotification extends Document {
   _id?: ObjectId;
   userId: ObjectId;
   title: string;
   message: string;
   type: "info" | "success" | "warning" | "error";
   category: "transaction" | "security" | "account" | "system" | "general";
   isRead: boolean;
   isGlobal: boolean; // true for notifications sent to all users
   metadata?: {
      transactionId?: ObjectId;
      accountId?: ObjectId;
      cardId?: ObjectId;
      amount?: number;
      recipientName?: string;
      location?: string;
      device?: string;
      [key: string]: any;
   };
   createdAt: Date;
   updatedAt: Date;
   createdBy?: ObjectId; // Admin who created this notification (for global notifications)
   expiresAt?: Date; // Optional expiration date for notifications
}

export interface UserNotificationDetails {
   id: string;
   userId: string;
   title: string;
   message: string;
   type: "info" | "success" | "warning" | "error";
   category: "transaction" | "security" | "account" | "system" | "general";
   isRead: boolean;
   isGlobal: boolean;
   metadata?: {
      transactionId?: string;
      accountId?: string;
      cardId?: string;
      amount?: number;
      recipientName?: string;
      location?: string;
      device?: string;
      [key: string]: any;
   };
   createdAt: Date;
   updatedAt: Date;
   createdBy?: string;
   expiresAt?: Date;
}

export interface CreateUserNotificationData {
   userId?: string; // Optional for global notifications
   title: string;
   message: string;
   type: "info" | "success" | "warning" | "error";
   category: "transaction" | "security" | "account" | "system" | "general";
   isGlobal?: boolean;
   metadata?: {
      transactionId?: string;
      accountId?: string;
      cardId?: string;
      amount?: number;
      recipientName?: string;
      location?: string;
      device?: string;
      [key: string]: any;
   };
   createdBy?: string; // Admin ID for global notifications
   expiresAt?: Date;
}

export interface UpdateUserNotificationData {
   isRead?: boolean;
   title?: string;
   message?: string;
   type?: "info" | "success" | "warning" | "error";
   category?: "transaction" | "security" | "account" | "system" | "general";
   metadata?: {
      transactionId?: string;
      accountId?: string;
      cardId?: string;
      amount?: number;
      recipientName?: string;
      location?: string;
      device?: string;
      [key: string]: any;
   };
   expiresAt?: Date;
}

// Notification filters for querying
export interface NotificationFilters {
   userId?: string;
   type?: "info" | "success" | "warning" | "error";
   category?: "transaction" | "security" | "account" | "system" | "general";
   isRead?: boolean;
   isGlobal?: boolean;
   dateFrom?: Date;
   dateTo?: Date;
   page?: number;
   limit?: number;
}
