"use client";

import { But<PERSON> } from "@/components/ui/button";
import { DashboardBreadcrumb } from "@/components/ui/dashboard-breadcrumb";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { TransactionPreviewModal } from "@/components/ui/transaction-preview-modal";
import { useAuth, useUserAccount } from "@/hooks";
import { createNewTransaction } from "@/lib/actions";
import { motion } from "framer-motion";
import {
   ArrowLeft,
   Bitcoin,
   CreditCard,
   DollarSign,
   Globe,
   MapPin,
   Smartphone,
   User,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function InternationalTransfer() {
   const { user } = useAuth();
   const { data: userAccount } = useUserAccount(user?.id);
   const [step, setStep] = useState<"method" | "form">("method");
   const [selectedMethod, setSelectedMethod] = useState("");
   const [showPreviewModal, setShowPreviewModal] = useState(false);
   const [isProcessing, setIsProcessing] = useState(false);
   const [showUnavailableDialog, setShowUnavailableDialog] = useState(false);
   const [unavailableMethod, setUnavailableMethod] = useState("");
   const [formData, setFormData] = useState({
      recipientName: "",
      recipientAddress: "",
      recipientCountry: "",
      bankName: "",
      swiftCode: "",
      accountNumber: "",
      amount: "",
      currency: "USD",
      purpose: "",
      // Additional fields for different payment methods
      email: "",
      phoneNumber: "",
      walletAddress: "",
      cryptoCurrency: "BTC",
   });

   const paymentMethods = [
      {
         id: "wire",
         name: "Wire Transfer",
         description: "Traditional SWIFT wire transfer",
         fee: "$15-25",
         time: "1-3 business days",
         icon: CreditCard,
         color: "bg-blue-500",
         logoColor: "#1e40af",
      },
      {
         id: "crypto",
         name: "Cryptocurrency",
         description: "Send using Bitcoin, Ethereum, or USDC",
         fee: "1-3%",
         time: "10-60 minutes",
         icon: Bitcoin,
         color: "bg-gradient-to-r from-orange-400 to-yellow-500",
         logoColor: "#f59e0b",
      },
      {
         id: "paypal",
         name: "PayPal",
         description: "Send via PayPal network",
         fee: "2.9% + $0.30",
         time: "Instant",
         icon: Smartphone,
         color: "bg-[#0070ba]",
         logoColor: "#0070ba",
      },
      {
         id: "wise",
         name: "Wise Transfer",
         description: "Low-cost international transfers",
         fee: "0.5-2%",
         time: "1-2 business days",
         icon: Globe,
         color: "bg-[#00b9ff]",
         logoColor: "#00b9ff",
      },
      {
         id: "cashapp",
         name: "Cash App",
         description: "Send via Cash App network",
         fee: "1.5%",
         time: "Instant",
         icon: DollarSign,
         color: "bg-[#00d632]",
         logoColor: "#00d632",
      },
      {
         id: "western-union",
         name: "Western Union",
         description: "Global money transfer service",
         fee: "$5-50",
         time: "Minutes to days",
         icon: Globe,
         color: "bg-yellow-500",
         logoColor: "#eab308",
      },
   ];

   const currencies = [
      { code: "USD", name: "US Dollar" },
      { code: "EUR", name: "Euro" },
      { code: "GBP", name: "British Pound" },
      { code: "CAD", name: "Canadian Dollar" },
      { code: "AUD", name: "Australian Dollar" },
      { code: "JPY", name: "Japanese Yen" },
   ];

   const cryptoCurrencies = [
      { code: "BTC", name: "Bitcoin" },
      { code: "ETH", name: "Ethereum" },
      { code: "USDC", name: "USD Coin" },
      { code: "USDT", name: "Tether" },
   ];

   const transferPurposes = [
      "Family Support",
      "Business Payment",
      "Education",
      "Investment",
      "Property Purchase",
      "Other",
   ];

   const handleInputChange = (
      e: React.ChangeEvent<
         HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
      >
   ) => {
      const { name, value } = e.target;
      setFormData((prev) => ({
         ...prev,
         [name]: value,
      }));
   };

   const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      setShowPreviewModal(true);
   };

   const handleMethodSelect = (methodId: string) => {
      if (methodId === "wire") {
         // Wire transfer is available - proceed to form
         setSelectedMethod(methodId);
         setStep("form");
      } else {
         // All other methods are unavailable
         const method = paymentMethods.find((m) => m.id === methodId);
         setUnavailableMethod(method?.name || "");
         setShowUnavailableDialog(true);
      }
   };

   const handleConfirmTransaction = async () => {
      if (!user || !userAccount) {
         toast.error("User account information not available");
         return;
      }

      setIsProcessing(true);
      try {
         // Create a pending transaction for wire transfer
         const result = await createNewTransaction({
            userId: user.id,
            accountId: userAccount.id,
            type: "debit",
            amount: parseFloat(formData.amount),
            currency: formData.currency,
            description:
               formData.purpose ||
               `International transfer to ${formData.recipientName}`,
            category: "International Transfer",
            merchant: formData.bankName,
            status: "pending", // Requires admin approval
            metadata: {
               transferType: "international",
               recipientName: formData.recipientName,
               // recipientAddress: formData.recipientAddress,
               // recipientCountry: formData.recipientCountry,
               recipientAccount: formData.accountNumber,
               // bankName: formData.bankName,
               // swiftCode: formData.swiftCode,
               paymentMethod: selectedMethod,
               // purpose: formData.purpose,
            },
         });

         if (result.success) {
            toast.success(
               `International wire transfer of $${formData.amount} to ${formData.recipientName} is pending approval. You will be notified once processed.`
            );
            setFormData({
               recipientName: "",
               recipientAddress: "",
               recipientCountry: "",
               bankName: "",
               swiftCode: "",
               accountNumber: "",
               amount: "",
               currency: "USD",
               purpose: "",
               email: "",
               phoneNumber: "",
               walletAddress: "",
               cryptoCurrency: "BTC",
            });
            setShowPreviewModal(false);
            setStep("method");
            setSelectedMethod("");
         } else {
            toast.error(result.message || "Failed to submit transfer");
         }
      } catch (error) {
         console.error("Transaction error:", error);
         toast.error("Failed to submit transfer. Please try again.");
      } finally {
         setIsProcessing(false);
      }
   };

   const calculateFee = (amount: number) => {
      if (amount < 1000) return 15;
      if (amount < 5000) return 20;
      return 25;
   };

   const fee = formData.amount ? calculateFee(parseFloat(formData.amount)) : 0;
   const total = formData.amount ? parseFloat(formData.amount) + fee : 0;

   return (
      <div>
         {/* Breadcrumbs */}
         <DashboardBreadcrumb
            items={[
               { label: "Transfers", href: "/dashboard" },
               { label: "International" },
            ]}
         />

         {/* Main Content */}
         <div className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               className="max-w-4xl mx-auto"
            >
               <div className="text-center mb-8">
                  <div className="mx-auto w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mb-4">
                     <Globe className="w-8 h-8 text-success" />
                  </div>
                  <h1 className="text-3xl font-bold text-foreground mb-2">
                     International Transfer
                  </h1>
                  <p className="text-muted-foreground">
                     {step === "method"
                        ? "Choose your preferred payment method for international transfers"
                        : `Send money internationally using ${
                             paymentMethods.find((m) => m.id === selectedMethod)
                                ?.name
                          }`}
                  </p>
               </div>

               {step === "method" ? (
                  /* Payment Method Selection */
                  <div className="space-y-6">
                     <div className="text-center mb-6">
                        <h2 className="text-xl font-semibold text-foreground mb-2">
                           Select Transfer Method
                        </h2>
                        <p className="text-muted-foreground text-sm">
                           Choose the payment method that works best for you
                        </p>
                     </div>

                     <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {paymentMethods.map((method, index) => {
                           const IconComponent = method.icon;
                           return (
                              <motion.div
                                 key={method.id}
                                 initial={{ opacity: 0, y: 20 }}
                                 animate={{ opacity: 1, y: 0 }}
                                 transition={{
                                    duration: 0.4,
                                    delay: index * 0.1,
                                 }}
                                 className="bank-card p-6 cursor-pointer hover:shadow-lg transition-all duration-200"
                                 onClick={() => handleMethodSelect(method.id)}
                              >
                                 <div className="flex items-start gap-4">
                                    <div
                                       className={`p-3 rounded-lg ${method.color} text-white flex-shrink-0`}
                                    >
                                       <IconComponent className="w-6 h-6" />
                                    </div>
                                    <div className="flex-1">
                                       <h3 className="font-semibold text-foreground mb-1">
                                          {method.name}
                                       </h3>
                                       <p className="text-sm text-muted-foreground mb-3">
                                          {method.description}
                                       </p>
                                       <div className="flex justify-between text-xs">
                                          <span className="text-muted-foreground">
                                             Fee:{" "}
                                             <span className="font-medium text-foreground">
                                                {method.fee}
                                             </span>
                                          </span>
                                          <span className="text-muted-foreground">
                                             Time:{" "}
                                             <span className="font-medium text-success">
                                                {method.time}
                                             </span>
                                          </span>
                                       </div>
                                    </div>
                                 </div>
                              </motion.div>
                           );
                        })}
                     </div>
                  </div>
               ) : (
                  /* Method-Specific Form */
                  <div className="space-y-6">
                     <div className="flex items-center gap-4 mb-6">
                        <Button
                           variant="ghost"
                           size="sm"
                           onClick={() => setStep("method")}
                           className="flex items-center gap-2"
                        >
                           <ArrowLeft size={16} />
                           Change Method
                        </Button>
                        <div className="flex items-center gap-2">
                           {(() => {
                              const method = paymentMethods.find(
                                 (m) => m.id === selectedMethod
                              );
                              const IconComponent = method?.icon || Globe;
                              return (
                                 <>
                                    <div
                                       className={`p-2 rounded ${method?.color} text-white`}
                                    >
                                       <IconComponent className="w-4 h-4" />
                                    </div>
                                    <span className="font-medium text-foreground">
                                       {method?.name}
                                    </span>
                                 </>
                              );
                           })()}
                        </div>
                     </div>

                     {/* Method-Specific Form Content */}
                     <div className="bank-card p-6">
                        <form onSubmit={handleSubmit} className="space-y-6">
                           {/* Recipient Information */}
                           <div className="space-y-4">
                              <h2 className="text-lg font-semibold text-foreground flex items-center gap-2">
                                 <User className="w-5 h-5" />
                                 Recipient Information
                              </h2>

                              <Input
                                 label="Recipient Name"
                                 name="recipientName"
                                 value={formData.recipientName}
                                 onChange={handleInputChange}
                                 placeholder="Enter recipient's full name"
                                 required
                              />

                              {/* Method-specific fields */}
                              {selectedMethod === "wire" && (
                                 <Input
                                    label="Recipient Address"
                                    name="recipientAddress"
                                    value={formData.recipientAddress}
                                    onChange={handleInputChange}
                                    placeholder="Enter recipient's address"
                                    required
                                 />
                              )}

                              {selectedMethod === "paypal" && (
                                 <Input
                                    label="PayPal Email"
                                    name="email"
                                    type="email"
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    placeholder="Enter recipient's PayPal email"
                                    required
                                 />
                              )}

                              {selectedMethod === "cashapp" && (
                                 <Input
                                    label="Phone Number or $Cashtag"
                                    name="phoneNumber"
                                    value={formData.phoneNumber}
                                    onChange={handleInputChange}
                                    placeholder="Enter phone number or $cashtag"
                                    required
                                 />
                              )}

                              {selectedMethod === "crypto" && (
                                 <>
                                    <div>
                                       <label className="text-sm font-medium text-foreground mb-2 block">
                                          Cryptocurrency
                                       </label>
                                       <select
                                          name="cryptoCurrency"
                                          value={formData.cryptoCurrency}
                                          onChange={handleInputChange}
                                          className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                                          required
                                       >
                                          {cryptoCurrencies.map((crypto) => (
                                             <option
                                                key={crypto.code}
                                                value={crypto.code}
                                             >
                                                {crypto.name} ({crypto.code})
                                             </option>
                                          ))}
                                       </select>
                                    </div>
                                    <Input
                                       label="Wallet Address"
                                       name="walletAddress"
                                       value={formData.walletAddress}
                                       onChange={handleInputChange}
                                       placeholder="Enter recipient's wallet address"
                                       required
                                    />
                                 </>
                              )}

                              {(selectedMethod === "wise" ||
                                 selectedMethod === "western-union" ||
                                 selectedMethod === "moneygram" ||
                                 selectedMethod === "remitly") && (
                                 <Input
                                    label="Phone Number"
                                    name="phoneNumber"
                                    value={formData.phoneNumber}
                                    onChange={handleInputChange}
                                    placeholder="Enter recipient's phone number"
                                    required
                                 />
                              )}

                              <Input
                                 label="Country"
                                 name="recipientCountry"
                                 value={formData.recipientCountry}
                                 onChange={handleInputChange}
                                 placeholder="Enter recipient's country"
                                 required
                              />
                           </div>

                           {/* Bank Information - Only for Wire Transfer */}
                           {selectedMethod === "wire" && (
                              <div className="space-y-4">
                                 <h2 className="text-lg font-semibold text-foreground flex items-center gap-2">
                                    <MapPin className="w-5 h-5" />
                                    Bank Information
                                 </h2>

                                 <Input
                                    label="Bank Name"
                                    name="bankName"
                                    value={formData.bankName}
                                    onChange={handleInputChange}
                                    placeholder="Enter recipient's bank name"
                                    required
                                 />

                                 <Input
                                    label="SWIFT Code"
                                    name="swiftCode"
                                    value={formData.swiftCode}
                                    onChange={handleInputChange}
                                    placeholder="Enter bank's SWIFT code"
                                    required
                                 />

                                 <Input
                                    label="Account Number/IBAN"
                                    name="accountNumber"
                                    value={formData.accountNumber}
                                    onChange={handleInputChange}
                                    placeholder="Enter account number or IBAN"
                                    required
                                 />
                              </div>
                           )}

                           {/* Transfer Details */}
                           <div className="space-y-4">
                              <h2 className="text-lg font-semibold text-foreground flex items-center gap-2">
                                 <DollarSign className="w-5 h-5" />
                                 Transfer Details
                              </h2>

                              <div className="grid grid-cols-2 gap-4">
                                 <Input
                                    label="Amount"
                                    name="amount"
                                    type="number"
                                    value={formData.amount}
                                    onChange={handleInputChange}
                                    placeholder="0.00"
                                    min="1"
                                    step="0.01"
                                    required
                                 />

                                 <div>
                                    <label className="text-sm font-medium text-foreground mb-2 block">
                                       Currency
                                    </label>
                                    <select
                                       name="currency"
                                       value={formData.currency}
                                       onChange={handleInputChange}
                                       className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                                       required
                                    >
                                       {currencies.map((currency) => (
                                          <option
                                             key={currency.code}
                                             value={currency.code}
                                          >
                                             {currency.code} - {currency.name}
                                          </option>
                                       ))}
                                    </select>
                                 </div>
                              </div>

                              <div>
                                 <label className="text-sm font-medium text-foreground mb-2 block">
                                    Purpose of Transfer
                                 </label>
                                 <select
                                    name="purpose"
                                    value={formData.purpose}
                                    onChange={handleInputChange}
                                    className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                                    required
                                 >
                                    <option value="">Select purpose</option>
                                    {transferPurposes.map((purpose) => (
                                       <option key={purpose} value={purpose}>
                                          {purpose}
                                       </option>
                                    ))}
                                 </select>
                              </div>
                           </div>

                           {/* Transfer Summary */}
                           {formData.amount && (
                              <div className="bg-muted/30 rounded-lg p-4">
                                 <h3 className="font-medium text-foreground mb-3">
                                    Transfer Summary
                                 </h3>
                                 <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                       <span className="text-muted-foreground">
                                          Transfer Amount:
                                       </span>
                                       <span className="font-medium">
                                          $
                                          {parseFloat(
                                             formData.amount || "0"
                                          ).toFixed(2)}
                                       </span>
                                    </div>
                                    <div className="flex justify-between">
                                       <span className="text-muted-foreground">
                                          Transfer Fee:
                                       </span>
                                       <span className="font-medium">
                                          ${fee.toFixed(2)}
                                       </span>
                                    </div>
                                    <div className="flex justify-between border-t border-border pt-2">
                                       <span className="text-muted-foreground">
                                          Total:
                                       </span>
                                       <span className="font-semibold">
                                          ${total.toFixed(2)}
                                       </span>
                                    </div>
                                    <div className="flex justify-between">
                                       <span className="text-muted-foreground">
                                          Processing Time:
                                       </span>
                                       <span className="font-medium">
                                          1-3 business days
                                       </span>
                                    </div>
                                 </div>
                              </div>
                           )}

                           {/* Submit Button */}
                           <Button
                              type="submit"
                              variant="premium"
                              size="lg"
                              className="w-full"
                              disabled={
                                 !formData.recipientName ||
                                 !formData.bankName ||
                                 !formData.swiftCode ||
                                 !formData.accountNumber ||
                                 !formData.amount ||
                                 !formData.purpose ||
                                 parseFloat(formData.amount) < 1
                              }
                           >
                              Send International Transfer
                           </Button>
                        </form>

                        {/* Security Notice */}
                        <div className="mt-6 p-4 bg-muted/30 rounded-lg">
                           <p className="text-sm text-muted-foreground text-center">
                              🔒 International transfers require additional
                              verification and compliance checks. You may be
                              contacted for additional documentation.
                           </p>
                        </div>
                     </div>
                  </div>
               )}
            </motion.div>
         </div>

         {/* Transaction Preview Modal */}
         <TransactionPreviewModal
            isOpen={showPreviewModal}
            onClose={() => setShowPreviewModal(false)}
            onConfirm={handleConfirmTransaction}
            isLoading={isProcessing}
            userId={user?.id}
            transaction={{
               type: "International Transfer",
               recipientName: formData.recipientName,
               accountNumber: formData.accountNumber,
               amount: formData.amount,
               description: formData.purpose,
               fee: `$${fee.toFixed(2)}`,
               total: `$${total.toFixed(2)}`,
               processingTime:
                  paymentMethods.find((m) => m.id === selectedMethod)?.time ||
                  "1-3 business days",
            }}
         />

         {/* Unavailable Method Dialog */}
         <Dialog
            open={showUnavailableDialog}
            onOpenChange={setShowUnavailableDialog}
         >
            <DialogContent>
               <DialogHeader>
                  <DialogTitle>Service Temporarily Unavailable</DialogTitle>
                  <DialogDescription>
                     Transfer through {unavailableMethod} is unavailable at the
                     moment. Please try again later or contact support.
                  </DialogDescription>
               </DialogHeader>
               <div className="flex justify-end gap-2 mt-4">
                  <Button
                     variant="outline"
                     onClick={() => setShowUnavailableDialog(false)}
                  >
                     Close
                  </Button>
                  <Button
                     onClick={() => {
                        setShowUnavailableDialog(false);
                        // In a real app, this would open a support chat or redirect to support
                        toast.info("Contact <NAME_EMAIL>");
                     }}
                  >
                     Contact Support
                  </Button>
               </div>
            </DialogContent>
         </Dialog>
      </div>
   );
}
