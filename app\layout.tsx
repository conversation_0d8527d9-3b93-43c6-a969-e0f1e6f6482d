import { QueryProvider } from "@/components/providers/query-provider";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { Toaster } from "@/components/ui/sonner";
import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
   variable: "--font-geist-sans",
   subsets: ["latin"],
});

const geistMono = Geist_Mono({
   variable: "--font-geist-mono",
   subsets: ["latin"],
});

export const metadata: Metadata = {
   title: "Paramount Bank - Secure Digital Banking",
   description: "Private banking application - not for public access",
   robots: {
      index: false,
      follow: false,
      nocache: true,
      googleBot: {
         index: false,
         follow: false,
         noimageindex: true,
         "max-video-preview": -1,
         "max-image-preview": "none",
         "max-snippet": -1,
      },
   },
   other: {
      "X-Robots-Tag":
         "noindex, nofollow, noarchive, nosnippet, noimageindex, nocache",
   },
};

export default function RootLayout({
   children,
}: Readonly<{
   children: React.ReactNode;
}>) {
   return (
      <html lang="en">
         <head>
            <meta
               name="robots"
               content="noindex, nofollow, noarchive, nosnippet, noimageindex, nocache"
            />
            <meta
               name="googlebot"
               content="noindex, nofollow, noarchive, nosnippet, noimageindex"
            />
            <meta
               name="bingbot"
               content="noindex, nofollow, noarchive, nosnippet, noimageindex"
            />
            <meta
               name="slurp"
               content="noindex, nofollow, noarchive, nosnippet, noimageindex"
            />
            <meta
               httpEquiv="X-Robots-Tag"
               content="noindex, nofollow, noarchive, nosnippet, noimageindex, nocache"
            />
            <meta name="referrer" content="no-referrer" />
            <meta name="format-detection" content="telephone=no" />
         </head>
         <body
            className={`${geistSans.variable} ${geistMono.variable} antialiased`}
         >
            <ErrorBoundary>
               <QueryProvider>{children}</QueryProvider>
               <Toaster />
            </ErrorBoundary>
         </body>
      </html>
   );
}
