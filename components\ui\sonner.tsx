"use client";

import { useTheme } from "next-themes";
import { Toaster as <PERSON><PERSON>, ToasterP<PERSON> } from "sonner";

const Toaster = ({ ...props }: ToasterProps) => {
   const { theme = "system" } = useTheme();

   return (
      <Sonner
         theme={theme as ToasterProps["theme"]}
         className="toaster group"
         style={
            {
               "--normal-bg": "var(--gradient-card)",
               "--normal-text": "var(--popover-foreground)",
               "--normal-border": "var(--border)",
            } as React.CSSProperties
         }
         {...props}
      />
   );
};

export { Toaster };
