import { cn } from "@/lib/utils";
import * as React from "react";

export interface InputProps
   extends React.InputHTMLAttributes<HTMLInputElement> {
   label?: string;
   error?: string;
   icon?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
   ({ className, type, label, error, icon, ...props }, ref) => {
      return (
         <div className="space-y-2">
            {label && (
               <label className="text-sm font-medium text-foreground">
                  {label}
               </label>
            )}
            <div className="relative">
               {icon && (
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground z-5">
                     {icon}
                  </div>
               )}
               <input
                  type={type}
                  className={cn(
                     "bank-input flex h-12 w-full rounded-lg px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",
                     icon && "pl-10",
                     error &&
                        "border-destructive focus:border-destructive focus:ring-destructive/20",
                     className
                  )}
                  ref={ref}
                  {...props}
               />
            </div>
            {error && <p className="text-sm text-destructive">{error}</p>}
         </div>
      );
   }
);
Input.displayName = "Input";

// PIN Input Component for banking authentication
export interface PinInputProps {
   length?: number;
   value?: string;
   onChange?: (value: string) => void;
   error?: string;
   disabled?: boolean;
   className?: string;
}

const PinInput = React.forwardRef<HTMLDivElement, PinInputProps>(
   ({ length = 4, value = "", onChange, error, disabled, className }, ref) => {
      const [pins, setPins] = React.useState<string[]>(
         Array(length)
            .fill("")
            .map((_, i) => value[i] || "")
      );
      const inputRefs = React.useRef<(HTMLInputElement | null)[]>([]);

      React.useEffect(() => {
         const newPins = Array(length)
            .fill("")
            .map((_, i) => value[i] || "");
         setPins(newPins);
      }, [value, length]);

      const handleChange = (index: number, newValue: string) => {
         if (disabled) return;

         // Only allow digits
         if (newValue && !/^\d$/.test(newValue)) return;

         const newPins = [...pins];
         newPins[index] = newValue;
         setPins(newPins);
         onChange?.(newPins.join(""));

         // Auto-focus next input
         if (newValue && index < length - 1) {
            inputRefs.current[index + 1]?.focus();
         }
      };

      const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
         if (e.key === "Backspace" && !pins[index] && index > 0) {
            inputRefs.current[index - 1]?.focus();
         }
      };

      const handlePaste = (e: React.ClipboardEvent) => {
         e.preventDefault();
         const pastedData = e.clipboardData.getData("text").replace(/\D/g, "");
         const newPins = Array(length).fill("");

         for (let i = 0; i < Math.min(pastedData.length, length); i++) {
            newPins[i] = pastedData[i];
         }

         setPins(newPins);
         onChange?.(newPins.join(""));

         // Focus the next empty input or the last input
         const nextEmptyIndex = newPins.findIndex((pin) => !pin);
         const focusIndex = nextEmptyIndex === -1 ? length - 1 : nextEmptyIndex;
         inputRefs.current[focusIndex]?.focus();
      };

      return (
         <div className={cn("space-y-2", className)} ref={ref}>
            <div className="flex gap-3 justify-center">
               {pins.map((pin, index) => (
                  <input
                     key={index}
                     ref={(el) => {
                        inputRefs.current[index] = el;
                     }}
                     type="text"
                     inputMode="numeric"
                     maxLength={1}
                     value={pin}
                     onChange={(e) => handleChange(index, e.target.value)}
                     onKeyDown={(e) => handleKeyDown(index, e)}
                     onPaste={handlePaste}
                     disabled={disabled}
                     className={cn(
                        "w-12 h-12 text-center text-lg font-semibold border-2 rounded-lg transition-all duration-200",
                        "focus:border-primary focus:ring-2 focus:ring-primary/20 focus:outline-none",
                        "disabled:opacity-50 disabled:cursor-not-allowed",
                        error ? "border-destructive" : "border-border",
                        pin ? "bg-primary/5" : "bg-background"
                     )}
                  />
               ))}
            </div>
            {error && (
               <p className="text-sm text-destructive text-center">{error}</p>
            )}
         </div>
      );
   }
);
PinInput.displayName = "PinInput";

export { Input, PinInput };
