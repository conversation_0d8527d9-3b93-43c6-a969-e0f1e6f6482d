"use server";

import { revalidatePath } from "next/cache";
import { AccountDetails, CreateAccountData, UpdateBalanceData } from "@/lib/models";
import { createAccount, getAccountByUserId, getAccountsByUserId, updateAccountBalance } from "@/lib/services/account";

/**
 * Server Action: Create a new account
 */
export async function createUserAccount(data: CreateAccountData): Promise<{ success: boolean; account?: AccountDetails; message?: string }> {
  try {
    const result = await createAccount(data);
    
    if (result.success) {
      // Revalidate account data
      revalidatePath("/dashboard");
      revalidatePath("/account-overview");
    }
    
    return result;
  } catch (error) {
    console.error("Create account action error:", error);
    return { success: false, message: "Internal server error" };
  }
}

/**
 * Server Action: Get user's primary account
 */
export async function getUserAccount(userId: string): Promise<{ success: boolean; account?: AccountDetails; message?: string }> {
  try {
    const result = await getAccountByUserId(userId);
    return result;
  } catch (error) {
    console.error("Get user account action error:", error);
    return { success: false, message: "Internal server error" };
  }
}

/**
 * Server Action: Get all user accounts
 */
export async function getUserAccounts(userId: string): Promise<{ success: boolean; accounts?: AccountDetails[]; message?: string }> {
  try {
    const result = await getAccountsByUserId(userId);
    return result;
  } catch (error) {
    console.error("Get user accounts action error:", error);
    return { success: false, message: "Internal server error" };
  }
}

/**
 * Server Action: Update account balance
 */
export async function updateBalance(data: UpdateBalanceData): Promise<{ success: boolean; newBalance?: number; message?: string }> {
  try {
    const result = await updateAccountBalance(data);
    
    if (result.success) {
      // Revalidate account and transaction data
      revalidatePath("/dashboard");
      revalidatePath("/account-overview");
      revalidatePath("/account-history");
    }
    
    return result;
  } catch (error) {
    console.error("Update balance action error:", error);
    return { success: false, message: "Internal server error" };
  }
}
