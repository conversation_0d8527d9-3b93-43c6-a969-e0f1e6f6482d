/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { PageLoader } from "@/components/ui/page-loader";
import { InlineQueryError } from "@/components/ui/query-error";
import { useAuth } from "@/hooks";
import { useAllBlockedUsers, useUnblockUser } from "@/hooks/use-user-block";
import { motion } from "framer-motion";
import { Calendar, Search, Shield, ShieldOff, User, Users } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

export default function BlockedUsersPage() {
   const router = useRouter();
   const { user: currentUser } = useAuth();
   const [searchTerm, setSearchTerm] = useState("");

   // Get blocked users
   const {
      data: blockedUsersData,
      isLoading,
      error,
      refetch,
   } = useAllBlockedUsers({
      search: searchTerm || undefined,
      limit: 50,
   });

   // Unblock user mutation
   const unblockUserMutation = useUnblockUser();

   const blockedUsers = blockedUsersData?.success
      ? blockedUsersData.blocks || []
      : [];

   const handleUnblockUser = async (userId: string, userName: string) => {
      if (!currentUser?.id) return;

      try {
         await unblockUserMutation.mutateAsync({
            userId,
            unblockedBy: currentUser.id,
         });

         toast.success(`${userName} has been unblocked successfully`);
      } catch (error) {
         console.error("Unblock user error:", error);
         toast.error(
            error instanceof Error ? error.message : "Failed to unblock user"
         );
      }
   };

   if (isLoading) {
      return <PageLoader message="Loading blocked users..." />;
   }

   return (
      <div className="container mx-auto px-4 py-8">
         {/* Header */}
         <div className="flex items-center justify-between mb-8">
            <div>
               <h1 className="text-3xl font-bold text-foreground">
                  Blocked Users
               </h1>
               <p className="text-muted-foreground">
                  Manage user account blocks and restrictions
               </p>
            </div>
            <div className="flex items-center gap-2">
               <Badge variant="destructive" className="text-sm">
                  <Shield className="w-4 h-4 mr-1" />
                  {blockedUsers.length} Blocked
               </Badge>
            </div>
         </div>

         {/* Search */}
         <Card className="mb-6">
            <CardContent className="pt-6">
               <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                     placeholder="Search blocked users by name, email, or block reason..."
                     value={searchTerm}
                     onChange={(e) => setSearchTerm(e.target.value)}
                     className="pl-10"
                  />
               </div>
            </CardContent>
         </Card>

         {/* Blocked Users List */}
         <Card>
            <CardHeader>
               <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Blocked Users ({blockedUsers.length})
               </CardTitle>
            </CardHeader>
            <CardContent>
               {error ? (
                  <InlineQueryError
                     error={error}
                     onRetry={refetch}
                     message="Failed to load blocked users"
                  />
               ) : blockedUsers.length === 0 ? (
                  <div className="text-center py-12">
                     <ShieldOff className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                     <h3 className="text-lg font-medium text-foreground mb-2">
                        No Blocked Users
                     </h3>
                     <p className="text-muted-foreground">
                        {searchTerm
                           ? "No blocked users match your search criteria."
                           : "There are currently no blocked users."}
                     </p>
                  </div>
               ) : (
                  <div className="space-y-4">
                     {blockedUsers.map((blockedUser: any) => (
                        <motion.div
                           key={blockedUser.id}
                           initial={{ opacity: 0, y: 20 }}
                           animate={{ opacity: 1, y: 0 }}
                           className="p-4 border rounded-lg hover:bg-muted/30 transition-colors"
                        >
                           <div className="flex items-center justify-between">
                              <div className="flex items-center gap-4">
                                 <div className="p-2 bg-destructive/10 rounded-lg">
                                    <User className="w-5 h-5 text-destructive" />
                                 </div>
                                 <div>
                                    <div className="flex items-center gap-2 mb-1">
                                       <h3 className="font-medium text-foreground">
                                          {blockedUser.user.firstName}{" "}
                                          {blockedUser.user.lastName}
                                       </h3>
                                       <Badge
                                          variant="destructive"
                                          className="text-xs"
                                       >
                                          Blocked
                                       </Badge>
                                    </div>
                                    <p className="text-sm text-muted-foreground mb-1">
                                       {blockedUser.user.email}
                                    </p>
                                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                       <div className="flex items-center gap-1">
                                          <Calendar className="w-3 h-3" />
                                          Blocked:{" "}
                                          {new Date(
                                             blockedUser.blockedAt
                                          ).toLocaleDateString()}
                                       </div>
                                       {blockedUser.blockReason && (
                                          <span>
                                             Reason: {blockedUser.blockReason}
                                          </span>
                                       )}
                                    </div>
                                 </div>
                              </div>
                              <div className="flex items-center gap-2">
                                 <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() =>
                                       router.push(
                                          `/admin/users/${blockedUser.userId}`
                                       )
                                    }
                                 >
                                    View Details
                                 </Button>
                                 <Button
                                    size="sm"
                                    variant="default"
                                    onClick={() =>
                                       handleUnblockUser(
                                          blockedUser.userId,
                                          `${blockedUser.user.firstName} ${blockedUser.user.lastName}`
                                       )
                                    }
                                    disabled={unblockUserMutation.isPending}
                                 >
                                    {unblockUserMutation.isPending ? (
                                       <>
                                          <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                          Unblocking...
                                       </>
                                    ) : (
                                       <>
                                          <ShieldOff className="w-4 h-4 mr-2" />
                                          Unblock
                                       </>
                                    )}
                                 </Button>
                              </div>
                           </div>

                           {/* Block Details */}
                           <div className="mt-4 p-3 bg-muted/30 rounded-lg">
                              <h4 className="font-medium text-sm text-foreground mb-2">
                                 {blockedUser.blockTitle}
                              </h4>
                              <p className="text-sm text-muted-foreground">
                                 {blockedUser.blockMessage}
                              </p>
                           </div>
                        </motion.div>
                     ))}
                  </div>
               )}
            </CardContent>
         </Card>
      </div>
   );
}
