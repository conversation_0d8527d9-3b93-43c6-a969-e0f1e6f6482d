import { Document, ObjectId } from "mongodb";

export interface Transaction extends Document {
   _id?: ObjectId;
   userId: ObjectId;
   accountId: ObjectId;
   transactionId: string; // Unique transaction identifier
   type: "credit" | "debit";
   amount: number;
   currency: string;
   description: string;
   merchant?: string;
   category: string;
   status: "pending" | "completed" | "failed" | "cancelled";
   reference?: string;
   balanceAfter: number;
   metadata?: {
      location?: string;
      paymentMethod?: string;
      cardId?: ObjectId;
      transferType?: "internal" | "external" | "international";
      recipientAccount?: string;
      recipientName?: string;
      createdByAdmin?: string;
      adminName?: string;
   };
   createdAt: Date;
   updatedAt: Date;
}

export interface TransactionDetails {
   id: string;
   userId: string;
   accountId: string;
   transactionId: string;
   type: "credit" | "debit";
   amount: number;
   currency: string;
   description: string;
   merchant?: string;
   category: string;
   status: "pending" | "completed" | "failed" | "cancelled";
   reference?: string;
   balanceAfter: number;
   metadata?: {
      location?: string;
      paymentMethod?: string;
      cardId?: string;
      transferType?: "internal" | "external" | "international";
      recipientAccount?: string;
      recipientName?: string;
      createdByAdmin?: string;
      adminName?: string;
   };
   date: Date;
}

export interface CreateTransactionData {
   userId: string;
   accountId: string;
   type: "credit" | "debit";
   amount: number;
   currency?: string;
   description: string;
   merchant?: string;
   category: string;
   status?: "pending" | "completed" | "failed" | "cancelled";
   reference?: string;
   customDate?: Date; // Optional custom date for admin-created transactions
   metadata?: {
      location?: string;
      paymentMethod?: string;
      cardId?: string;
      transferType?: "internal" | "external" | "international";
      recipientAccount?: string;
      recipientName?: string;
      createdByAdmin?: string;
      adminName?: string;
   };
}

export interface TransactionFilters {
   userId: string;
   accountId?: string;
   type?: "credit" | "debit";
   category?: string;
   status?: "pending" | "completed" | "failed" | "cancelled";
   dateFrom?: Date;
   dateTo?: Date;
   limit?: number;
   offset?: number;
}
