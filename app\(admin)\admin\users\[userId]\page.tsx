/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PageLoader } from "@/components/ui/page-loader";
import { InlineQueryError } from "@/components/ui/query-error";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { TransactionStatusBadge } from "@/components/ui/transaction-status-badge";
import { useAuth } from "@/hooks";
import {
   useComprehensiveUserDetails,
   useUpdateUserProfileByAdmin,
} from "@/hooks/use-admin";
import {
   useBlockUser,
   useUnblockUser,
   useUserBlockStatus,
} from "@/hooks/use-user-block";
import { useUpdateUserMetrics, useUserMetrics } from "@/hooks/use-user-metrics";
import { motion } from "framer-motion";
import {
   ArrowLeft,
   Calendar,
   CreditCard,
   DollarSign,
   Edit,
   History,
   Mail,
   MapPin,
   Phone,
   Save,
   Shield,
   User,
   Wallet,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { use, useState } from "react";
import { toast } from "sonner";

interface UserEditPageProps {
   params: Promise<{ userId: string }>;
}

export default function UserEditPage({ params: oldParams }: UserEditPageProps) {
   const params = use(oldParams);
   const router = useRouter();
   const { user: currentUser } = useAuth();
   const [isEditing, setIsEditing] = useState(false);

   // Get comprehensive user details
   const {
      data: userDetails,
      isLoading,
      error,
      refetch,
   } = useComprehensiveUserDetails(params.userId);

   // Update user profile mutation
   const updateUserMutation = useUpdateUserProfileByAdmin();

   // Get user metrics
   const {
      data: userMetrics,
      isLoading: isLoadingMetrics,
      error: metricsError,
   } = useUserMetrics(params.userId);

   // Update user metrics mutation
   const updateMetricsMutation = useUpdateUserMetrics();

   // Get user block status
   const { data: userBlockStatus, isLoading: isLoadingBlockStatus } =
      useUserBlockStatus(params.userId);

   // Block/unblock mutations
   const blockUserMutation = useBlockUser();
   const unblockUserMutation = useUnblockUser();

   // Form state for editing
   const [editForm, setEditForm] = useState({
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      country: "",
      accountType: "",
      role: "user" as "user" | "admin",
      verificationStatus: "verified" as "verified" | "pending" | "unverified",
   });

   // Metrics form state
   const [metricsForm, setMetricsForm] = useState({
      monthlySpending: 0,
      monthlyIncome: 0,
      transactionCount: 0,
      spendingChange: 0,
      incomeChange: 0,
   });

   const [isEditingMetrics, setIsEditingMetrics] = useState(false);

   // Initialize form when data loads
   useState(() => {
      if (userDetails?.user) {
         setEditForm({
            firstName: userDetails.user.firstName,
            lastName: userDetails.user.lastName,
            email: userDetails.user.email,
            phoneNumber: userDetails.user.phoneNumber,
            country: userDetails.user.country,
            accountType: userDetails.user.accountType,
            role: userDetails.user.role,
            verificationStatus: userDetails.user.verificationStatus,
         });
      }
   });

   // Initialize metrics form when data loads
   useState(() => {
      if (userMetrics) {
         setMetricsForm({
            monthlySpending: userMetrics.monthlySpending,
            monthlyIncome: userMetrics.monthlyIncome,
            transactionCount: userMetrics.transactionCount,
            spendingChange: userMetrics.spendingChange,
            incomeChange: userMetrics.incomeChange,
         });
      }
   });

   const handleSave = async () => {
      if (!currentUser?.id || !userDetails?.user) return;

      try {
         await updateUserMutation.mutateAsync({
            userId: params.userId,
            updates: editForm,
            adminId: currentUser.id,
         });

         toast.success("User details updated successfully");
         setIsEditing(false);
      } catch (error) {
         console.error("Save error:", error);
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to update user details"
         );
      }
   };

   const handleCancel = () => {
      if (userDetails?.user) {
         setEditForm({
            firstName: userDetails.user.firstName,
            lastName: userDetails.user.lastName,
            email: userDetails.user.email,
            phoneNumber: userDetails.user.phoneNumber,
            country: userDetails.user.country,
            accountType: userDetails.user.accountType,
            role: userDetails.user.role,
            verificationStatus: userDetails.user.verificationStatus,
         });
      }
      setIsEditing(false);
   };

   const handleSaveMetrics = async () => {
      if (!currentUser?.id) return;

      try {
         await updateMetricsMutation.mutateAsync({
            userId: params.userId,
            data: {
               ...metricsForm,
               updatedBy: currentUser.id,
            },
         });

         toast.success("User metrics updated successfully");
         setIsEditingMetrics(false);
      } catch (error) {
         console.error("Save metrics error:", error);
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to update user metrics"
         );
      }
   };

   const handleCancelMetrics = () => {
      if (userMetrics) {
         setMetricsForm({
            monthlySpending: userMetrics.monthlySpending,
            monthlyIncome: userMetrics.monthlyIncome,
            transactionCount: userMetrics.transactionCount,
            spendingChange: userMetrics.spendingChange,
            incomeChange: userMetrics.incomeChange,
         });
      }
      setIsEditingMetrics(false);
   };

   const handleBlockUser = async (blockData: {
      title: string;
      message: string;
      reason?: string;
   }) => {
      if (!currentUser?.id || !userDetails?.user) return;

      try {
         await blockUserMutation.mutateAsync({
            userId: params.userId,
            blockTitle: blockData.title,
            blockMessage: blockData.message,
            blockReason: blockData.reason,
            blockedBy: currentUser.id,
         });

         toast.success("User has been blocked successfully");
      } catch (error) {
         console.error("Block user error:", error);
         toast.error(
            error instanceof Error ? error.message : "Failed to block user"
         );
      }
   };

   const handleUnblockUser = async () => {
      if (!currentUser?.id || !userDetails?.user) return;

      try {
         await unblockUserMutation.mutateAsync({
            userId: params.userId,
            unblockedBy: currentUser.id,
         });

         toast.success("User has been unblocked successfully");
      } catch (error) {
         console.error("Unblock user error:", error);
         toast.error(
            error instanceof Error ? error.message : "Failed to unblock user"
         );
      }
   };

   if (isLoading) {
      return <PageLoader message="Loading user details..." />;
   }

   if (error) {
      return (
         <div className="container mx-auto px-4 py-8">
            <InlineQueryError
               error={error}
               onRetry={refetch}
               message="Failed to load user details"
            />
         </div>
      );
   }

   if (!userDetails?.user) {
      return (
         <div className="container mx-auto px-4 py-8">
            <div className="text-center">
               <h1 className="text-2xl font-bold text-foreground mb-4">
                  User Not Found
               </h1>
               <p className="text-muted-foreground mb-6">
                  The requested user could not be found.
               </p>
               <Button onClick={() => router.push("/admin/users")}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Users
               </Button>
            </div>
         </div>
      );
   }

   const { user, account, cards = [], recentTransactions = [] } = userDetails;

   return (
      <div className="container mx-auto px-4 py-8">
         {/* Header */}
         <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
               <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push("/admin/users")}
               >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Users
               </Button>
               <div>
                  <h1 className="text-3xl font-bold text-foreground">
                     {user.firstName} {user.lastName}
                  </h1>
                  <p className="text-muted-foreground">User ID: {user.id}</p>
               </div>
            </div>
            <div className="flex items-center gap-2">
               {!isEditing ? (
                  <Button onClick={() => setIsEditing(true)}>
                     <Edit className="w-4 h-4 mr-2" />
                     Edit User
                  </Button>
               ) : (
                  <>
                     <Button variant="outline" onClick={handleCancel}>
                        Cancel
                     </Button>
                     <Button
                        onClick={handleSave}
                        disabled={updateUserMutation.isPending}
                     >
                        {updateUserMutation.isPending ? (
                           <>
                              <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                              Saving...
                           </>
                        ) : (
                           <>
                              <Save className="w-4 h-4 mr-2" />
                              Save Changes
                           </>
                        )}
                     </Button>
                  </>
               )}
            </div>
         </div>

         <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - User Profile */}
            <div className="lg:col-span-2 space-y-6">
               {/* User Information Card */}
               <Card>
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <User className="w-5 h-5" />
                        User Information
                     </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                     <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                           <Label htmlFor="firstName">First Name</Label>
                           {isEditing ? (
                              <Input
                                 id="firstName"
                                 value={editForm.firstName}
                                 onChange={(e) =>
                                    setEditForm((prev) => ({
                                       ...prev,
                                       firstName: e.target.value,
                                    }))
                                 }
                              />
                           ) : (
                              <p className="text-sm text-muted-foreground mt-1">
                                 {user.firstName}
                              </p>
                           )}
                        </div>
                        <div>
                           <Label htmlFor="lastName">Last Name</Label>
                           {isEditing ? (
                              <Input
                                 id="lastName"
                                 value={editForm.lastName}
                                 onChange={(e) =>
                                    setEditForm((prev) => ({
                                       ...prev,
                                       lastName: e.target.value,
                                    }))
                                 }
                              />
                           ) : (
                              <p className="text-sm text-muted-foreground mt-1">
                                 {user.lastName}
                              </p>
                           )}
                        </div>
                        <div>
                           <Label htmlFor="email">Email</Label>
                           {isEditing ? (
                              <Input
                                 id="email"
                                 type="email"
                                 value={editForm.email}
                                 onChange={(e) =>
                                    setEditForm((prev) => ({
                                       ...prev,
                                       email: e.target.value,
                                    }))
                                 }
                              />
                           ) : (
                              <div className="flex items-center gap-2 mt-1">
                                 <Mail className="w-4 h-4 text-muted-foreground" />
                                 <p className="text-sm text-muted-foreground">
                                    {user.email}
                                 </p>
                              </div>
                           )}
                        </div>
                        <div>
                           <Label htmlFor="phoneNumber">Phone Number</Label>
                           {isEditing ? (
                              <Input
                                 id="phoneNumber"
                                 value={editForm.phoneNumber}
                                 onChange={(e) =>
                                    setEditForm((prev) => ({
                                       ...prev,
                                       phoneNumber: e.target.value,
                                    }))
                                 }
                              />
                           ) : (
                              <div className="flex items-center gap-2 mt-1">
                                 <Phone className="w-4 h-4 text-muted-foreground" />
                                 <p className="text-sm text-muted-foreground">
                                    {user.phoneNumber}
                                 </p>
                              </div>
                           )}
                        </div>
                        <div>
                           <Label htmlFor="country">Country</Label>
                           {isEditing ? (
                              <Input
                                 id="country"
                                 value={editForm.country}
                                 onChange={(e) =>
                                    setEditForm((prev) => ({
                                       ...prev,
                                       country: e.target.value,
                                    }))
                                 }
                              />
                           ) : (
                              <div className="flex items-center gap-2 mt-1">
                                 <MapPin className="w-4 h-4 text-muted-foreground" />
                                 <p className="text-sm text-muted-foreground">
                                    {user.country}
                                 </p>
                              </div>
                           )}
                        </div>
                        <div>
                           <Label htmlFor="accountType">Account Type</Label>
                           {isEditing ? (
                              <Input
                                 id="accountType"
                                 value={editForm.accountType}
                                 onChange={(e) =>
                                    setEditForm((prev) => ({
                                       ...prev,
                                       accountType: e.target.value,
                                    }))
                                 }
                              />
                           ) : (
                              <p className="text-sm text-muted-foreground mt-1">
                                 {user.accountType}
                              </p>
                           )}
                        </div>
                     </div>

                     <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                        <div>
                           <Label htmlFor="role">Role</Label>
                           {isEditing ? (
                              <Select
                                 value={editForm.role}
                                 onValueChange={(value: "user" | "admin") =>
                                    setEditForm((prev) => ({
                                       ...prev,
                                       role: value,
                                    }))
                                 }
                              >
                                 <SelectTrigger>
                                    <SelectValue />
                                 </SelectTrigger>
                                 <SelectContent>
                                    <SelectItem value="user">User</SelectItem>
                                    <SelectItem value="admin">Admin</SelectItem>
                                 </SelectContent>
                              </Select>
                           ) : (
                              <div className="flex items-center gap-2 mt-1">
                                 <Badge
                                    variant={
                                       user.role === "admin"
                                          ? "destructive"
                                          : "outline"
                                    }
                                 >
                                    <Shield className="w-3 h-3 mr-1" />
                                    {!user.role
                                       ? "User"
                                       : user.role?.charAt(0).toUpperCase() +
                                         user.role?.slice(1)}
                                 </Badge>
                              </div>
                           )}
                        </div>
                        <div>
                           <Label htmlFor="verificationStatus">
                              Verification Status
                           </Label>
                           {isEditing ? (
                              <Select
                                 value={editForm.verificationStatus}
                                 onValueChange={(
                                    value: "verified" | "pending" | "unverified"
                                 ) =>
                                    setEditForm((prev) => ({
                                       ...prev,
                                       verificationStatus: value,
                                    }))
                                 }
                              >
                                 <SelectTrigger>
                                    <SelectValue />
                                 </SelectTrigger>
                                 <SelectContent>
                                    <SelectItem value="verified">
                                       Verified
                                    </SelectItem>
                                    <SelectItem value="pending">
                                       Pending
                                    </SelectItem>
                                    <SelectItem value="unverified">
                                       Unverified
                                    </SelectItem>
                                 </SelectContent>
                              </Select>
                           ) : (
                              <div className="flex items-center gap-2 mt-1">
                                 <Badge
                                    variant={
                                       user.verificationStatus === "verified"
                                          ? "default"
                                          : user.verificationStatus ===
                                            "pending"
                                          ? "secondary"
                                          : "destructive"
                                    }
                                 >
                                    {user.verificationStatus
                                       .charAt(0)
                                       .toUpperCase() +
                                       user.verificationStatus.slice(1)}
                                 </Badge>
                              </div>
                           )}
                        </div>
                     </div>
                  </CardContent>
               </Card>

               {/* User Metrics Card */}
               <Card>
                  <CardHeader>
                     <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center gap-2">
                           <DollarSign className="w-5 h-5" />
                           Dashboard Metrics
                        </CardTitle>
                        {!isEditingMetrics ? (
                           <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setIsEditingMetrics(true)}
                           >
                              <Edit className="w-4 h-4 mr-2" />
                              Edit Metrics
                           </Button>
                        ) : (
                           <div className="flex items-center gap-2">
                              <Button
                                 size="sm"
                                 variant="outline"
                                 onClick={handleCancelMetrics}
                              >
                                 Cancel
                              </Button>
                              <Button
                                 size="sm"
                                 onClick={handleSaveMetrics}
                                 disabled={updateMetricsMutation.isPending}
                              >
                                 {updateMetricsMutation.isPending ? (
                                    <>
                                       <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                       Saving...
                                    </>
                                 ) : (
                                    <>
                                       <Save className="w-4 h-4 mr-2" />
                                       Save
                                    </>
                                 )}
                              </Button>
                           </div>
                        )}
                     </div>
                  </CardHeader>
                  <CardContent>
                     {isLoadingMetrics ? (
                        <div className="space-y-4">
                           {Array.from({ length: 3 }).map((_, i) => (
                              <div key={i} className="flex justify-between">
                                 <div className="h-4 w-24 bg-muted rounded animate-pulse" />
                                 <div className="h-4 w-16 bg-muted rounded animate-pulse" />
                              </div>
                           ))}
                        </div>
                     ) : metricsError ? (
                        <p className="text-sm text-destructive">
                           Failed to load metrics
                        </p>
                     ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                           <div>
                              <Label htmlFor="monthlySpending">
                                 Monthly Spending
                              </Label>
                              {isEditingMetrics ? (
                                 <Input
                                    id="monthlySpending"
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    value={metricsForm.monthlySpending}
                                    onChange={(e) =>
                                       setMetricsForm((prev) => ({
                                          ...prev,
                                          monthlySpending:
                                             parseFloat(e.target.value) || 0,
                                       }))
                                    }
                                 />
                              ) : (
                                 <p className="text-sm text-muted-foreground mt-1">
                                    $
                                    {userMetrics?.monthlySpending.toLocaleString() ||
                                       "0.00"}
                                 </p>
                              )}
                           </div>
                           <div>
                              <Label htmlFor="monthlyIncome">
                                 Monthly Income
                              </Label>
                              {isEditingMetrics ? (
                                 <Input
                                    id="monthlyIncome"
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    value={metricsForm.monthlyIncome}
                                    onChange={(e) =>
                                       setMetricsForm((prev) => ({
                                          ...prev,
                                          monthlyIncome:
                                             parseFloat(e.target.value) || 0,
                                       }))
                                    }
                                 />
                              ) : (
                                 <p className="text-sm text-muted-foreground mt-1">
                                    $
                                    {userMetrics?.monthlyIncome.toLocaleString() ||
                                       "0.00"}
                                 </p>
                              )}
                           </div>
                           <div>
                              <Label htmlFor="transactionCount">
                                 Transaction Count
                              </Label>
                              {isEditingMetrics ? (
                                 <Input
                                    id="transactionCount"
                                    type="number"
                                    min="0"
                                    value={metricsForm.transactionCount}
                                    onChange={(e) =>
                                       setMetricsForm((prev) => ({
                                          ...prev,
                                          transactionCount:
                                             parseInt(e.target.value) || 0,
                                       }))
                                    }
                                 />
                              ) : (
                                 <p className="text-sm text-muted-foreground mt-1">
                                    {userMetrics?.transactionCount || 0}
                                 </p>
                              )}
                           </div>
                           <div>
                              <Label htmlFor="spendingChange">
                                 Spending Change (%)
                              </Label>
                              {isEditingMetrics ? (
                                 <Input
                                    id="spendingChange"
                                    type="number"
                                    step="0.1"
                                    value={metricsForm.spendingChange}
                                    onChange={(e) =>
                                       setMetricsForm((prev) => ({
                                          ...prev,
                                          spendingChange:
                                             parseFloat(e.target.value) || 0,
                                       }))
                                    }
                                 />
                              ) : (
                                 <p className="text-sm text-muted-foreground mt-1">
                                    {userMetrics &&
                                    userMetrics?.spendingChange > 0
                                       ? "+"
                                       : ""}
                                    {(userMetrics &&
                                       userMetrics?.spendingChange) ||
                                       0}
                                    %
                                 </p>
                              )}
                           </div>
                           <div>
                              <Label htmlFor="incomeChange">
                                 Income Change (%)
                              </Label>
                              {isEditingMetrics ? (
                                 <Input
                                    id="incomeChange"
                                    type="number"
                                    step="0.1"
                                    value={metricsForm.incomeChange}
                                    onChange={(e) =>
                                       setMetricsForm((prev) => ({
                                          ...prev,
                                          incomeChange:
                                             parseFloat(e.target.value) || 0,
                                       }))
                                    }
                                 />
                              ) : (
                                 <p className="text-sm text-muted-foreground mt-1">
                                    {userMetrics &&
                                    userMetrics?.incomeChange > 0
                                       ? "+"
                                       : ""}
                                    {(userMetrics &&
                                       userMetrics?.incomeChange) ||
                                       0}
                                    %
                                 </p>
                              )}
                           </div>
                        </div>
                     )}
                  </CardContent>
               </Card>
            </div>

            {/* Right Column - Account & Quick Stats */}
            <div className="space-y-6">
               {/* Account Information */}
               {account && (
                  <Card>
                     <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                           <Wallet className="w-5 h-5" />
                           Account Details
                        </CardTitle>
                     </CardHeader>
                     <CardContent className="space-y-3">
                        <div>
                           <Label className="text-xs text-muted-foreground">
                              Account Number
                           </Label>
                           <p className="font-mono text-sm">
                              {account.accountNumber}
                           </p>
                        </div>
                        <div>
                           <Label className="text-xs text-muted-foreground">
                              Available Balance
                           </Label>
                           <p className="text-2xl font-bold text-success">
                              ${account.availableBalance.toLocaleString()}
                           </p>
                        </div>
                        <div>
                           <Label className="text-xs text-muted-foreground">
                              Account Status
                           </Label>
                           <Badge
                              variant={
                                 account.accountStatus === "Active"
                                    ? "default"
                                    : "destructive"
                              }
                           >
                              {account.accountStatus}
                           </Badge>
                        </div>
                        <div>
                           <Label className="text-xs text-muted-foreground">
                              Date Opened
                           </Label>
                           <div className="flex items-center gap-2">
                              <Calendar className="w-4 h-4 text-muted-foreground" />
                              <p className="text-sm">
                                 {new Date(
                                    account.dateOpened
                                 ).toLocaleDateString()}
                              </p>
                           </div>
                        </div>
                     </CardContent>
                  </Card>
               )}

               {/* Quick Stats */}
               <Card>
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <DollarSign className="w-5 h-5" />
                        Quick Stats
                     </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                     <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                           Total Cards
                        </span>
                        <span className="font-medium">{cards.length}</span>
                     </div>
                     <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                           Recent Transactions
                        </span>
                        <span className="font-medium">
                           {recentTransactions.length}
                        </span>
                     </div>
                  </CardContent>
               </Card>

               {/* Block Status */}
               <Card>
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <Shield className="w-5 h-5" />
                        Block Status
                     </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                     {isLoadingBlockStatus ? (
                        <div className="space-y-2">
                           <div className="h-4 w-20 bg-muted rounded animate-pulse" />
                           <div className="h-3 w-32 bg-muted rounded animate-pulse" />
                        </div>
                     ) : userBlockStatus?.isBlocked ? (
                        <>
                           <div>
                              <Badge variant="destructive" className="mb-2">
                                 <Shield className="w-3 h-3 mr-1" />
                                 Blocked
                              </Badge>
                              <p className="text-sm font-medium text-foreground">
                                 {userBlockStatus.blockTitle}
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">
                                 Blocked:{" "}
                                 {new Date(
                                    userBlockStatus.blockedAt
                                 ).toLocaleDateString()}
                              </p>
                              {userBlockStatus.blockReason && (
                                 <p className="text-xs text-muted-foreground">
                                    Reason: {userBlockStatus.blockReason}
                                 </p>
                              )}
                           </div>
                           <div className="p-3 bg-destructive/10 rounded-lg">
                              <p className="text-sm text-foreground">
                                 {userBlockStatus.blockMessage}
                              </p>
                           </div>
                           <Button
                              size="sm"
                              variant="default"
                              onClick={handleUnblockUser}
                              disabled={unblockUserMutation.isPending}
                              className="w-full"
                           >
                              {unblockUserMutation.isPending ? (
                                 <>
                                    <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                    Unblocking...
                                 </>
                              ) : (
                                 <>
                                    <Shield className="w-4 h-4 mr-2" />
                                    Unblock User
                                 </>
                              )}
                           </Button>
                        </>
                     ) : (
                        <>
                           <div>
                              <Badge variant="default" className="mb-2">
                                 <Shield className="w-3 h-3 mr-1" />
                                 Active
                              </Badge>
                              <p className="text-sm text-muted-foreground">
                                 User account is active and not blocked
                              </p>
                           </div>
                           <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => {
                                 // For now, use a simple template - in a real app, you'd show a modal with templates
                                 handleBlockUser({
                                    title: "Account Suspended",
                                    message:
                                       "Your account has been temporarily suspended. Please contact support for more information.",
                                    reason: "Administrative action",
                                 });
                              }}
                              disabled={blockUserMutation.isPending}
                              className="w-full"
                           >
                              {blockUserMutation.isPending ? (
                                 <>
                                    <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                    Blocking...
                                 </>
                              ) : (
                                 <>
                                    <Shield className="w-4 h-4 mr-2" />
                                    Block User
                                 </>
                              )}
                           </Button>
                        </>
                     )}
                  </CardContent>
               </Card>
            </div>
         </div>

         {/* Cards Section */}
         {cards.length > 0 && (
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6, delay: 0.2 }}
               className="mt-8"
            >
               <Card>
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <CreditCard className="w-5 h-5" />
                        User Cards ({cards.length})
                     </CardTitle>
                  </CardHeader>
                  <CardContent>
                     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {cards.map((card: any) => (
                           <div
                              key={card._id}
                              className="p-4 border rounded-lg hover:bg-muted/30 transition-colors"
                           >
                              <div className="flex items-center justify-between mb-2">
                                 <Badge
                                    variant={
                                       card.cardTier === "black"
                                          ? "default"
                                          : card.cardTier === "platinum"
                                          ? "secondary"
                                          : "outline"
                                    }
                                 >
                                    {card.cardTier?.charAt(0).toUpperCase() +
                                       card.cardTier?.slice(1)}
                                 </Badge>
                                 <Badge
                                    variant={
                                       card.status === "active"
                                          ? "default"
                                          : card.status === "blocked"
                                          ? "destructive"
                                          : "secondary"
                                    }
                                 >
                                    {card.status?.charAt(0).toUpperCase() +
                                       card.status?.slice(1)}
                                 </Badge>
                              </div>
                              <p className="font-medium text-sm mb-1">
                                 {card.cardName}
                              </p>
                              <p className="text-xs text-muted-foreground mb-2">
                                 {card.cardType?.charAt(0).toUpperCase() +
                                    card.cardType?.slice(1)}{" "}
                                 Card
                              </p>
                              <div className="text-xs text-muted-foreground space-y-1">
                                 <div className="flex justify-between">
                                    <span>Daily Limit:</span>
                                    <span>
                                       ${card.dailyLimit?.toLocaleString()}
                                    </span>
                                 </div>
                                 <div className="flex justify-between">
                                    <span>Monthly Limit:</span>
                                    <span>
                                       ${card.monthlyLimit?.toLocaleString()}
                                    </span>
                                 </div>
                              </div>
                           </div>
                        ))}
                     </div>
                  </CardContent>
               </Card>
            </motion.div>
         )}

         {/* Recent Transactions Section */}
         {recentTransactions.length > 0 && (
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6, delay: 0.3 }}
               className="mt-8"
            >
               <Card>
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <History className="w-5 h-5" />
                        Recent Transactions ({recentTransactions.length})
                     </CardTitle>
                  </CardHeader>
                  <CardContent>
                     <div className="space-y-4">
                        {recentTransactions.map((transaction) => (
                           <div
                              key={transaction.id}
                              className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/30 transition-colors"
                           >
                              <div className="flex items-center gap-4">
                                 <div
                                    className={`p-2 rounded-lg ${
                                       transaction.type === "credit"
                                          ? "bg-success/10"
                                          : "bg-muted"
                                    }`}
                                 >
                                    <DollarSign
                                       className={`w-5 h-5 ${
                                          transaction.type === "credit"
                                             ? "text-success"
                                             : "text-muted-foreground"
                                       }`}
                                    />
                                 </div>
                                 <div>
                                    <div className="flex items-center gap-2 mb-1">
                                       <p className="font-medium text-sm">
                                          {transaction.description}
                                       </p>
                                       <TransactionStatusBadge
                                          status={transaction.status}
                                          showIcon={false}
                                          className="text-xs"
                                       />
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                       {transaction.merchant &&
                                          `${transaction.merchant} • `}
                                       {transaction.category} •{" "}
                                       {new Date(
                                          transaction.date
                                       ).toLocaleDateString()}
                                    </p>
                                 </div>
                              </div>
                              <div className="text-right">
                                 <p
                                    className={`font-medium ${
                                       transaction.type === "credit"
                                          ? "text-success"
                                          : "text-foreground"
                                    }`}
                                 >
                                    {transaction.type === "credit" ? "+" : "-"}$
                                    {transaction.amount.toLocaleString()}
                                 </p>
                                 <p className="text-xs text-muted-foreground">
                                    Balance: $
                                    {transaction.balanceAfter.toLocaleString()}
                                 </p>
                              </div>
                           </div>
                        ))}
                     </div>
                  </CardContent>
               </Card>
            </motion.div>
         )}
      </div>
   );
}
