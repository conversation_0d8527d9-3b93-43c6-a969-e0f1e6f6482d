"use client";

import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { Bell } from "lucide-react";
import * as React from "react";
import { Badge } from "./badge";
import { Button } from "./button";
import { Separator } from "./separator";

export interface Notification {
   id: string;
   title: string;
   message: string;
   type: "info" | "success" | "warning" | "error";
   timestamp: Date;
   read: boolean;
}

export interface NotificationDropdownProps {
   notifications: Notification[];
   onMarkAsRead: (id: string) => void;
   onMarkAllAsRead: () => void;
   onViewAll: () => void;
   className?: string;
}

const NotificationDropdown = React.forwardRef<
   HTMLDivElement,
   NotificationDropdownProps
>(
   (
      { notifications, onMarkAsRead, onMarkAllAsRead, onViewAll, className },
      ref
   ) => {
      const [isOpen, setIsOpen] = React.useState(false);
      const unreadCount = notifications.filter((n) => !n.read).length;
      const recentNotifications = notifications.slice(0, 5);

      const getTypeColor = (type: Notification["type"]) => {
         switch (type) {
            case "success":
               return "text-success";
            case "warning":
               return "text-warning";
            case "error":
               return "text-destructive";
            default:
               return "text-primary";
         }
      };

      const formatTime = (date: Date) => {
         const now = new Date();
         const diff = now.getTime() - date.getTime();
         const minutes = Math.floor(diff / 60000);
         const hours = Math.floor(diff / 3600000);
         const days = Math.floor(diff / 86400000);

         if (minutes < 1) return "Just now";
         if (minutes < 60) return `${minutes}m ago`;
         if (hours < 24) return `${hours}h ago`;
         return `${days}d ago`;
      };

      return (
         <div className={cn("relative", className)} ref={ref}>
            <Button
               variant="ghost"
               size="icon"
               onClick={() => setIsOpen(!isOpen)}
               className="relative"
            >
               <Bell className="h-5 w-5" />
               {unreadCount > 0 && (
                  <Badge
                     variant="destructive"
                     className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs"
                  >
                     {unreadCount > 9 ? "9+" : unreadCount}
                  </Badge>
               )}
            </Button>

            <AnimatePresence>
               {isOpen && (
                  <>
                     <div
                        className="fixed inset-0 z-40"
                        onClick={() => setIsOpen(false)}
                     />
                     <motion.div
                        initial={{ opacity: 0, scale: 0.95, y: -10 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.95, y: -10 }}
                        transition={{ duration: 0.2 }}
                        className="absolute right-0 top-full mt-2 w-80 bg-popover border border-border rounded-lg shadow-lg z-50"
                     >
                        <div className="p-4">
                           <div className="flex items-center justify-between mb-3">
                              <h3 className="font-semibold text-foreground">
                                 Notifications
                              </h3>
                              {unreadCount > 0 && (
                                 <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={onMarkAllAsRead}
                                    className="text-xs"
                                 >
                                    Mark all as read
                                 </Button>
                              )}
                           </div>

                           <div className="space-y-2 max-h-80 overflow-y-auto">
                              {recentNotifications.length === 0 ? (
                                 <div className="text-center py-8 text-muted-foreground">
                                    <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                    <p>No notifications</p>
                                 </div>
                              ) : (
                                 recentNotifications.map((notification) => (
                                    <div
                                       key={notification.id}
                                       className={cn(
                                          "p-3 rounded-lg border transition-colors cursor-pointer",
                                          notification.read
                                             ? "bg-muted/30 border-border/50"
                                             : "bg-primary/5 border-primary/20"
                                       )}
                                       onClick={() =>
                                          onMarkAsRead(notification.id)
                                       }
                                    >
                                       <div className="flex items-start gap-3">
                                          <div
                                             className={cn(
                                                "w-2 h-2 rounded-full mt-2 flex-shrink-0",
                                                notification.read
                                                   ? "bg-muted-foreground"
                                                   : getTypeColor(
                                                        notification.type
                                                     )
                                             )}
                                          />
                                          <div className="flex-1 min-w-0">
                                             <div className="flex items-center justify-between">
                                                <p className="font-medium text-sm text-foreground truncate">
                                                   {notification.title}
                                                </p>
                                                <span className="text-xs text-muted-foreground flex-shrink-0 ml-2">
                                                   {formatTime(
                                                      notification.timestamp
                                                   )}
                                                </span>
                                             </div>
                                             <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                                {notification.message}
                                             </p>
                                          </div>
                                       </div>
                                    </div>
                                 ))
                              )}
                           </div>

                           {notifications.length > 5 && (
                              <>
                                 <Separator className="my-3" />
                                 <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={onViewAll}
                                    className="w-full"
                                 >
                                    View All Notifications
                                 </Button>
                              </>
                           )}
                        </div>
                     </motion.div>
                  </>
               )}
            </AnimatePresence>
         </div>
      );
   }
);

NotificationDropdown.displayName = "NotificationDropdown";

export { NotificationDropdown };
