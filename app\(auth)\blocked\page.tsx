"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/hooks";
import { useUserBlockStatus } from "@/hooks/use-user-block";
import { motion } from "framer-motion";
import {
   AlertTriangle,
   HelpCircle,
   Mail,
   Phone,
   Shield,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function BlockedPage() {
   const router = useRouter();
   const { user, logout } = useAuth();
   
   // Get user block status
   const {
      data: userBlockStatus,
      isLoading,
   } = useUserBlockStatus(user?.id);

   // Redirect if user is not blocked
   useEffect(() => {
      if (!isLoading && !userBlockStatus) {
         router.push("/dashboard");
      }
   }, [isLoading, userBlockStatus, router]);

   const handleLogout = async () => {
      await logout();
      router.push("/login");
   };

   if (isLoading) {
      return (
         <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
         </div>
      );
   }

   if (!userBlockStatus) {
      return null; // Will redirect
   }

   return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted p-4">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="w-full max-w-md"
         >
            <Card className="border-destructive/20 shadow-lg">
               <CardHeader className="text-center pb-4">
                  <div className="mx-auto mb-4 p-3 bg-destructive/10 rounded-full w-fit">
                     <Shield className="w-8 h-8 text-destructive" />
                  </div>
                  <CardTitle className="text-xl font-bold text-destructive">
                     {userBlockStatus.blockTitle}
                  </CardTitle>
               </CardHeader>
               <CardContent className="space-y-6">
                  {/* Block Message */}
                  <div className="p-4 bg-muted/50 rounded-lg border-l-4 border-destructive">
                     <div className="flex items-start gap-3">
                        <AlertTriangle className="w-5 h-5 text-destructive mt-0.5 flex-shrink-0" />
                        <p className="text-sm text-foreground leading-relaxed">
                           {userBlockStatus.blockMessage}
                        </p>
                     </div>
                  </div>

                  {/* Block Details */}
                  <div className="space-y-3">
                     <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Account:</span>
                        <span className="font-medium">{user?.email}</span>
                     </div>
                     <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Blocked Date:</span>
                        <span className="font-medium">
                           {new Date(userBlockStatus.blockedAt).toLocaleDateString()}
                        </span>
                     </div>
                     {userBlockStatus.blockReason && (
                        <div className="flex justify-between text-sm">
                           <span className="text-muted-foreground">Reason:</span>
                           <span className="font-medium">{userBlockStatus.blockReason}</span>
                        </div>
                     )}
                  </div>

                  {/* Contact Support */}
                  <div className="p-4 bg-primary/5 rounded-lg border">
                     <div className="flex items-center gap-2 mb-3">
                        <HelpCircle className="w-5 h-5 text-primary" />
                        <h3 className="font-medium text-foreground">Need Help?</h3>
                     </div>
                     <p className="text-sm text-muted-foreground mb-4">
                        If you believe this is an error or need assistance, please contact our support team.
                     </p>
                     <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm">
                           <Mail className="w-4 h-4 text-muted-foreground" />
                           <span className="text-foreground"><EMAIL></span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                           <Phone className="w-4 h-4 text-muted-foreground" />
                           <span className="text-foreground">+****************</span>
                        </div>
                     </div>
                  </div>

                  {/* Actions */}
                  <div className="space-y-3">
                     <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => window.open("mailto:<EMAIL>", "_blank")}
                     >
                        <Mail className="w-4 h-4 mr-2" />
                        Contact Support
                     </Button>
                     <Button
                        variant="secondary"
                        className="w-full"
                        onClick={handleLogout}
                     >
                        Sign Out
                     </Button>
                  </div>

                  {/* Footer */}
                  <div className="text-center pt-4 border-t">
                     <p className="text-xs text-muted-foreground">
                        This action was taken to protect your account and our platform.
                     </p>
                  </div>
               </CardContent>
            </Card>
         </motion.div>
      </div>
   );
}
