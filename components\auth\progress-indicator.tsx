import { motion } from "framer-motion";
import { Check } from "lucide-react";

interface Step {
   id: number;
   title: string;
   description: string;
}

interface ProgressIndicatorProps {
   steps: Step[];
   currentStep: number;
}

export const ProgressIndicator = ({
   steps,
   currentStep,
}: ProgressIndicatorProps) => {
   return (
      <div className="w-full max-w-3xl mx-auto mb-8">
         <div className="flex items-center justify-between">
            {steps.map((step, index) => {
               const stepNumber = index + 1;
               const isActive = stepNumber === currentStep;
               const isCompleted = stepNumber < currentStep;
               // const isUpcoming = stepNumber > currentStep;

               return (
                  <div key={step.id} className="flex items-center flex-1">
                     <div className="flex flex-col items-center">
                        <motion.div
                           className={`bank-progress-step ${
                              isActive
                                 ? "active"
                                 : isCompleted
                                 ? "completed"
                                 : "inactive"
                           }`}
                           initial={false}
                           animate={{
                              scale: isActive ? 1.1 : 1,
                           }}
                           transition={{ duration: 0.2 }}
                        >
                           {isCompleted ? (
                              <Check size={16} />
                           ) : (
                              <span>{stepNumber}</span>
                           )}
                        </motion.div>
                        <div className="mt-2 text-center">
                           <div
                              className={`text-sm font-medium ${
                                 isActive
                                    ? "text-primary"
                                    : isCompleted
                                    ? "text-success"
                                    : "text-muted-foreground"
                              }`}
                           >
                              {step.title}
                           </div>
                           <div className="text-xs text-muted-foreground hidden sm:block">
                              {step.description}
                           </div>
                        </div>
                     </div>

                     {index < steps.length - 1 && (
                        <div className="flex-1 h-px mx-4 relative">
                           <div className="absolute inset-0 bg-border" />
                           <motion.div
                              className="absolute inset-0 bg-primary origin-left"
                              initial={false}
                              animate={{
                                 scaleX: stepNumber < currentStep ? 1 : 0,
                              }}
                              transition={{ duration: 0.3 }}
                           />
                        </div>
                     )}
                  </div>
               );
            })}
         </div>
      </div>
   );
};
