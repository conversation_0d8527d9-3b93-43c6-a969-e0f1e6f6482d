import {
   Account,
   AccountDetails,
   CreateAccountData,
   UpdateBalanceData,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { generateAccountNumber } from "@/lib/utils/auth";
import { ObjectId, OptionalId } from "mongodb";

/**
 * Create a new account for a user
 */
export async function createAccount(
   data: CreateAccountData
): Promise<{ success: boolean; account?: AccountDetails; message?: string }> {
   try {
      const accountsCollection = await getCollection<Account>("accounts");

      const accountNumber = generateAccountNumber();
      const routingNumber = "*********"; // Standard routing number for demo

      const newAccount: OptionalId<Account> = {
         userId: new ObjectId(data.userId),
         accountNumber,
         routingNumber,
         accountType: data.accountType,
         accountStatus: "Active",
         availableBalance: data.initialDeposit || 0,
         totalBalance: data.initialDeposit || 0,
         currency: data.currency || "USD",
         dateOpened: new Date(),
         branch: data.branch || "Paramount Bank - Downtown Branch",
         createdAt: new Date(),
         updatedAt: new Date(),
      };

      const result = await accountsCollection.insertOne(newAccount);

      if (result.insertedId) {
         const accountDetails: AccountDetails = {
            id: result.insertedId.toString(),
            userId: data.userId,
            accountNumber: newAccount.accountNumber,
            routingNumber: newAccount.routingNumber,
            accountType: newAccount.accountType,
            accountStatus: newAccount.accountStatus,
            availableBalance: newAccount.availableBalance,
            totalBalance: newAccount.totalBalance,
            currency: newAccount.currency,
            dateOpened: newAccount.dateOpened,
            branch: newAccount.branch,
         };

         return { success: true, account: accountDetails };
      }

      return { success: false, message: "Failed to create account" };
   } catch (error) {
      console.error("Error creating account:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Get account by user ID
 */
export async function getAccountByUserId(
   userId: string
): Promise<{ success: boolean; account?: AccountDetails; message?: string }> {
   try {
      const accountsCollection = await getCollection<Account>("accounts");
      const account = await accountsCollection.findOne({
         userId: new ObjectId(userId),
      });

      if (!account) {
         return { success: false, message: "Account not found" };
      }

      const accountDetails: AccountDetails = {
         id: account._id!.toString(),
         userId: userId,
         accountNumber: account.accountNumber,
         routingNumber: account.routingNumber,
         accountType: account.accountType,
         accountStatus: account.accountStatus,
         availableBalance: account.availableBalance,
         totalBalance: account.totalBalance,
         currency: account.currency,
         dateOpened: account.dateOpened,
         branch: account.branch,
      };

      return { success: true, account: accountDetails };
   } catch (error) {
      console.error("Error getting account:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Update account balance
 */
export async function updateAccountBalance(
   data: UpdateBalanceData
): Promise<{ success: boolean; newBalance?: number; message?: string }> {
   try {
      const accountsCollection = await getCollection<Account>("accounts");

      const account = await accountsCollection.findOne({
         _id: new ObjectId(data.accountId),
      });
      if (!account) {
         return { success: false, message: "Account not found" };
      }

      let newBalance: number;
      if (data.type === "credit") {
         newBalance = account.availableBalance + data.amount;
      } else {
         newBalance = account.availableBalance - data.amount;
         if (newBalance < 0) {
            return { success: false, message: "Insufficient funds" };
         }
      }

      const result = await accountsCollection.findOneAndUpdate(
         { _id: new ObjectId(data.accountId) },
         {
            $set: {
               availableBalance: newBalance,
               totalBalance: newBalance,
               updatedAt: new Date(),
            },
         },
         { returnDocument: "after" }
      );

      if (!result) {
         return { success: false, message: "Failed to update balance" };
      }

      return { success: true, newBalance };
   } catch (error) {
      console.error("Error updating account balance:", error);
      return { success: false, message: "Internal server error" };
   }
}

/**
 * Get multiple accounts by user ID (for users with multiple accounts)
 */
export async function getAccountsByUserId(userId: string): Promise<{
   success: boolean;
   accounts?: AccountDetails[];
   message?: string;
}> {
   try {
      const accountsCollection = await getCollection<Account>("accounts");
      const accounts = await accountsCollection
         .find({ userId: new ObjectId(userId) })
         .toArray();

      const accountDetails: AccountDetails[] = accounts.map((account) => ({
         id: account._id!.toString(),
         userId: userId,
         accountNumber: account.accountNumber,
         routingNumber: account.routingNumber,
         accountType: account.accountType,
         accountStatus: account.accountStatus,
         availableBalance: account.availableBalance,
         totalBalance: account.totalBalance,
         currency: account.currency,
         dateOpened: account.dateOpened,
         branch: account.branch,
      }));

      return { success: true, accounts: accountDetails };
   } catch (error) {
      console.error("Error getting accounts:", error);
      return { success: false, message: "Internal server error" };
   }
}
