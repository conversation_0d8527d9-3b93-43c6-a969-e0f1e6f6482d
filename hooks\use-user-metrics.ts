"use client";

import { getUserMetricsAction, updateUserMetricsAction } from "@/lib/actions";
import { UpdateUserMetricsData } from "@/lib/models";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

/**
 * Hook to get user metrics
 */
export function useUserMetrics(userId: string | undefined) {
   return useQuery({
      queryKey: ["user-metrics", userId],
      queryFn: async () => {
         if (!userId) throw new Error("User ID is required");
         const result = await getUserMetricsAction(userId);
         if (!result.success) {
            throw new Error(result.message || "Failed to get user metrics");
         }
         return result.metrics!;
      },
      enabled: !!userId,
      staleTime: 2 * 60 * 1000, // 2 minutes
   });
}

/**
 * Hook to update user metrics
 */
export function useUpdateUserMetrics() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async ({
         userId,
         data,
      }: {
         userId: string;
         data: UpdateUserMetricsData;
      }) => {
         const result = await updateUserMetricsAction(userId, data);
         if (!result.success) {
            throw new Error(result.message || "Failed to update user metrics");
         }
         return result.metrics!;
      },
      onSuccess: (data, variables) => {
         // Invalidate and refetch user metrics
         queryClient.invalidateQueries({ queryKey: ["user-metrics", variables.userId] });
         // Also invalidate user details if it exists
         queryClient.invalidateQueries({ queryKey: ["admin", "user-details", variables.userId] });
      },
   });
}
