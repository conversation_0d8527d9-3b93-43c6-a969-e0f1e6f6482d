"use client";

import { Input } from "@/components/ui/input";
import { AnimatePresence, motion } from "framer-motion";
import { CreditCard } from "lucide-react";
import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";

// Step 3 Schema
export const accountSetupSchema = z.object({
   accountType: z.string().min(1, "Please select an account type"),
   transactionPin: z.string().regex(/^\d{4}$/, "PIN must be exactly 4 digits"),
});

export type AccountSetupForm = z.infer<typeof accountSetupSchema>;

const accountTypes = [
   {
      id: "checking",
      name: "Checking Account",
      description: "For everyday banking and transactions",
   },
   {
      id: "savings",
      name: "Savings Account",
      description: "Earn interest on your deposits",
   },
];

const extendedAccountTypes = [
   {
      id: "fixed-deposit",
      name: "Fixed Deposit Account",
      description: "Higher returns with fixed terms",
   },
   {
      id: "current",
      name: "Current Account",
      description: "For business transactions",
   },
   {
      id: "crypto",
      name: "Cryptocurrency Account",
      description: "Trade and store digital assets",
   },
   {
      id: "business",
      name: "Business Account",
      description: "For small to medium businesses",
   },
   {
      id: "non-resident",
      name: "Non-Resident Account",
      description: "For international customers",
   },
   {
      id: "corporate",
      name: "Corporate Business Account",
      description: "For large corporations",
   },
   {
      id: "investment",
      name: "Investment Account",
      description: "Portfolio management and trading",
   },
];

interface AccountSetupStepProps {
   form: UseFormReturn<AccountSetupForm>;
}

export function AccountSetupStep({ form }: AccountSetupStepProps) {
   const [showExtendedTypes, setShowExtendedTypes] = useState(false);

   return (
      <motion.div
         key="step3"
         initial={{ opacity: 0, x: 20 }}
         animate={{ opacity: 1, x: 0 }}
         exit={{ opacity: 0, x: -20 }}
         className="space-y-6"
      >
         <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-foreground mb-2">
               Account Setup
            </h2>
            <p className="text-muted-foreground">
               Choose your account type and preferences
            </p>
         </div>

         <div className="space-y-4">
            <label className="text-sm font-medium text-foreground">
               Account Type
            </label>

            {/* Primary account types */}
            <div className="grid gap-3">
               {accountTypes.map((type) => (
                  <label
                     key={type.id}
                     className="flex items-start gap-3 p-4 border-2 border-border rounded-lg cursor-pointer hover:border-primary transition-colors"
                  >
                     <input
                        type="radio"
                        value={type.name}
                        {...form.register("accountType")}
                        className="mt-1"
                     />
                     <div>
                        <div className="font-medium text-foreground">
                           {type.name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                           {type.description}
                        </div>
                     </div>
                  </label>
               ))}
            </div>

            {/* Show more account types */}
            <button
               type="button"
               onClick={() => setShowExtendedTypes(!showExtendedTypes)}
               className="text-primary hover:text-primary-light font-medium text-sm transition-colors"
            >
               {showExtendedTypes
                  ? "Show Less"
                  : "Show More Account Types"}
            </button>

            <AnimatePresence>
               {showExtendedTypes && (
                  <motion.div
                     initial={{ opacity: 0, height: 0 }}
                     animate={{ opacity: 1, height: "auto" }}
                     exit={{ opacity: 0, height: 0 }}
                     className="grid gap-3"
                  >
                     {extendedAccountTypes.map((type) => (
                        <label
                           key={type.id}
                           className="flex items-start gap-3 p-4 border-2 border-border rounded-lg cursor-pointer hover:border-primary transition-colors"
                        >
                           <input
                              type="radio"
                              value={type.name}
                              {...form.register("accountType")}
                              className="mt-1"
                           />
                           <div>
                              <div className="font-medium text-foreground">
                                 {type.name}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                 {type.description}
                              </div>
                           </div>
                        </label>
                     ))}
                  </motion.div>
               )}
            </AnimatePresence>

            {form.formState.errors.accountType && (
               <p className="text-sm text-destructive">
                  {form.formState.errors.accountType.message}
               </p>
            )}
         </div>

         <Input
            label="Transaction PIN"
            type="password"
            placeholder="Enter 4-digit PIN"
            maxLength={4}
            icon={<CreditCard size={18} />}
            error={form.formState.errors.transactionPin?.message}
            {...form.register("transactionPin")}
         />
      </motion.div>
   );
}
