/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth, useChangeUserRole, useUsers } from "@/hooks";
import { motion } from "framer-motion";
import {
   AlertTriangle,
   CheckCircle,
   Crown,
   Filter,
   Shield,
   User,
   UserPlus,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function RoleManagement() {
   const { user } = useAuth();
   const [searchTerm, setSearchTerm] = useState("");
   const [roleFilter, setRoleFilter] = useState<"all" | "user" | "admin">(
      "all"
   );
   const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
   const [bulkRole, setBulkRole] = useState<"user" | "admin" | "">("");
   const [bulkReason, setBulkReason] = useState("");
   const [showCreateAdmin, setShowCreateAdmin] = useState(false);

   // New admin form state
   const [newAdminData, setNewAdminData] = useState({
      email: "",
      firstName: "",
      lastName: "",
      username: "",
      phoneNumber: "",
      country: "",
      accountType: "Administrator",
      password: "",
      transactionPin: "",
   });

   const changeRoleMutation = useChangeUserRole();

   // Get users with filtering
   const {
      data: usersData,
      isLoading,
      error,
      refetch,
   } = useUsers({
      search: searchTerm,
      role: roleFilter === "all" ? undefined : roleFilter,
      limit: 50,
   });

   const users = usersData?.users || [];

   const handleRoleChange = async (
      userId: string,
      newRole: "user" | "admin",
      reason?: string
   ) => {
      if (!user) return;

      try {
         await changeRoleMutation.mutateAsync({
            userId,
            role: newRole,
            adminId: user.id,
            reason,
         });

         toast.success(`User role updated to ${newRole}`);
      } catch (error) {
         toast.error(
            error instanceof Error
               ? error.message
               : "Failed to update user role"
         );
      }
   };

   const handleBulkRoleChange = async () => {
      if (!bulkRole || selectedUsers.length === 0 || !user) {
         toast.error("Please select users and a role");
         return;
      }

      if (!bulkReason.trim()) {
         toast.error("Please provide a reason for the bulk action");
         return;
      }

      try {
         // Process bulk updates
         const promises = selectedUsers.map((userId) =>
            changeRoleMutation.mutateAsync({
               userId,
               role: bulkRole,
               adminId: user.id,
               reason: bulkReason,
            })
         );

         await Promise.all(promises);

         toast.success(
            `${selectedUsers.length} users updated to ${bulkRole} role`
         );
         setSelectedUsers([]);
         setBulkRole("");
         setBulkReason("");
      } catch (error) {
         console.error("Bulk role change error:", error);
         toast.error("Failed to perform bulk role change");
      }
   };

   const handleCreateAdmin = async () => {
      // In real app, this would call the API to create a new admin user
      console.log("Create new admin:", newAdminData);
      toast.success("New admin created successfully");
      setShowCreateAdmin(false);
      setNewAdminData({
         email: "",
         firstName: "",
         lastName: "",
         username: "",
         phoneNumber: "",
         country: "",
         accountType: "Administrator",
         password: "",
         transactionPin: "",
      });
   };

   const handleSelectUser = (userId: string, checked: boolean) => {
      if (checked) {
         setSelectedUsers((prev) => [...prev, userId]);
      } else {
         setSelectedUsers((prev) => prev.filter((id) => id !== userId));
      }
   };

   const handleSelectAll = (checked: boolean) => {
      if (checked) {
         setSelectedUsers(filteredUsers.map((user) => user.id));
      } else {
         setSelectedUsers([]);
      }
   };

   const getRoleBadge = (role?: string) => {
      return (
         <Badge variant={role === "admin" ? "destructive" : "outline"}>
            {role === "admin" ? (
               <Crown className="h-3 w-3 mr-1" />
            ) : (
               <User className="h-3 w-3 mr-1" />
            )}
            {role ? role.charAt(0).toUpperCase() + role.slice(1) : "User"}
         </Badge>
      );
   };

   const filteredUsers = users.filter((user) => {
      const matchesSearch =
         user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
         user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
         user.email.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesSearch;
   });

   const stats = {
      total: users.length,
      admins: users.filter((u) => u.role === "admin").length,
      regularUsers: users.filter((u) => u.role === "user").length,
   };

   return (
      <div className="container mx-auto px-4 py-8">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
         >
            {/* Header */}
            <div className="mb-8">
               <div className="flex items-center justify-between">
                  <div>
                     <h1 className="text-3xl font-bold text-foreground mb-2">
                        Role Management
                     </h1>
                     <p className="text-muted-foreground">
                        Change user roles, register new admins, and manage admin
                        permissions.
                     </p>
                  </div>
                  <Button onClick={() => setShowCreateAdmin(true)}>
                     <UserPlus className="h-4 w-4 mr-2" />
                     Create Admin
                  </Button>
               </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <Shield className="h-5 w-5 text-muted-foreground" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Total Users
                           </p>
                           <p className="text-2xl font-bold">{stats.total}</p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <Crown className="h-5 w-5 text-purple-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Administrators
                           </p>
                           <p className="text-2xl font-bold text-purple-600">
                              {stats.admins}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
               <Card>
                  <CardContent className="p-4">
                     <div className="flex items-center gap-2">
                        <User className="h-5 w-5 text-blue-600" />
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Regular Users
                           </p>
                           <p className="text-2xl font-bold text-blue-600">
                              {stats.regularUsers}
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
            </div>

            {/* Create Admin Form */}
            {showCreateAdmin && (
               <Card className="mb-6">
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <UserPlus className="h-5 w-5" />
                        Create New Administrator
                     </CardTitle>
                  </CardHeader>
                  <CardContent>
                     <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                           <Label htmlFor="email">Email *</Label>
                           <Input
                              id="email"
                              type="email"
                              placeholder="<EMAIL>"
                              value={newAdminData.email}
                              onChange={(e) =>
                                 setNewAdminData((prev) => ({
                                    ...prev,
                                    email: e.target.value,
                                 }))
                              }
                              className="mt-1"
                           />
                        </div>
                        <div>
                           <Label htmlFor="username">Username *</Label>
                           <Input
                              id="username"
                              placeholder="admin_username"
                              value={newAdminData.username}
                              onChange={(e) =>
                                 setNewAdminData((prev) => ({
                                    ...prev,
                                    username: e.target.value,
                                 }))
                              }
                              className="mt-1"
                           />
                        </div>
                        <div>
                           <Label htmlFor="firstName">First Name *</Label>
                           <Input
                              id="firstName"
                              placeholder="John"
                              value={newAdminData.firstName}
                              onChange={(e) =>
                                 setNewAdminData((prev) => ({
                                    ...prev,
                                    firstName: e.target.value,
                                 }))
                              }
                              className="mt-1"
                           />
                        </div>
                        <div>
                           <Label htmlFor="lastName">Last Name *</Label>
                           <Input
                              id="lastName"
                              placeholder="Doe"
                              value={newAdminData.lastName}
                              onChange={(e) =>
                                 setNewAdminData((prev) => ({
                                    ...prev,
                                    lastName: e.target.value,
                                 }))
                              }
                              className="mt-1"
                           />
                        </div>
                        <div>
                           <Label htmlFor="phoneNumber">Phone Number</Label>
                           <Input
                              id="phoneNumber"
                              placeholder="+1234567890"
                              value={newAdminData.phoneNumber}
                              onChange={(e) =>
                                 setNewAdminData((prev) => ({
                                    ...prev,
                                    phoneNumber: e.target.value,
                                 }))
                              }
                              className="mt-1"
                           />
                        </div>
                        <div>
                           <Label htmlFor="country">Country</Label>
                           <Input
                              id="country"
                              placeholder="United States"
                              value={newAdminData.country}
                              onChange={(e) =>
                                 setNewAdminData((prev) => ({
                                    ...prev,
                                    country: e.target.value,
                                 }))
                              }
                              className="mt-1"
                           />
                        </div>
                        <div>
                           <Label htmlFor="password">Password *</Label>
                           <Input
                              id="password"
                              type="password"
                              placeholder="Secure password"
                              value={newAdminData.password}
                              onChange={(e) =>
                                 setNewAdminData((prev) => ({
                                    ...prev,
                                    password: e.target.value,
                                 }))
                              }
                              className="mt-1"
                           />
                        </div>
                        <div>
                           <Label htmlFor="transactionPin">
                              Transaction PIN *
                           </Label>
                           <Input
                              id="transactionPin"
                              type="password"
                              placeholder="4-digit PIN"
                              maxLength={4}
                              value={newAdminData.transactionPin}
                              onChange={(e) =>
                                 setNewAdminData((prev) => ({
                                    ...prev,
                                    transactionPin: e.target.value,
                                 }))
                              }
                              className="mt-1"
                           />
                        </div>
                        <div className="md:col-span-2">
                           <div className="flex gap-4">
                              <Button
                                 onClick={handleCreateAdmin}
                                 disabled={
                                    !newAdminData.email ||
                                    !newAdminData.username ||
                                    !newAdminData.firstName ||
                                    !newAdminData.lastName ||
                                    !newAdminData.password ||
                                    !newAdminData.transactionPin
                                 }
                              >
                                 Create Administrator
                              </Button>
                              <Button
                                 variant="outline"
                                 onClick={() => setShowCreateAdmin(false)}
                              >
                                 Cancel
                              </Button>
                           </div>
                        </div>
                     </div>
                  </CardContent>
               </Card>
            )}

            {/* Filters */}
            <Card className="mb-6">
               <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                     <Filter className="h-5 w-5" />
                     Search & Filter
                  </CardTitle>
               </CardHeader>
               <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                     <div>
                        <Label htmlFor="search">Search Users</Label>
                        <Input
                           id="search"
                           placeholder="Search by name or email..."
                           value={searchTerm}
                           onChange={(e) => setSearchTerm(e.target.value)}
                           className="mt-1"
                        />
                     </div>
                     <div>
                        <Label htmlFor="role-filter">Role Filter</Label>
                        <Select
                           value={roleFilter}
                           onValueChange={(value: any) => setRoleFilter(value)}
                        >
                           <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Filter by role" />
                           </SelectTrigger>
                           <SelectContent>
                              <SelectItem value="all">All Roles</SelectItem>
                              <SelectItem value="user">Users</SelectItem>
                              <SelectItem value="admin">
                                 Administrators
                              </SelectItem>
                           </SelectContent>
                        </Select>
                     </div>
                  </div>
               </CardContent>
            </Card>

            {/* Bulk Actions */}
            {selectedUsers.length > 0 && (
               <Card className="mb-6">
                  <CardHeader>
                     <CardTitle className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5" />
                        Bulk Role Change ({selectedUsers.length} selected)
                     </CardTitle>
                  </CardHeader>
                  <CardContent>
                     <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                           <Label htmlFor="bulk-role">New Role</Label>
                           <Select
                              value={bulkRole}
                              onValueChange={(value: any) => setBulkRole(value)}
                           >
                              <SelectTrigger className="mt-1">
                                 <SelectValue placeholder="Select role" />
                              </SelectTrigger>
                              <SelectContent>
                                 <SelectItem value="user">User</SelectItem>
                                 <SelectItem value="admin">
                                    Administrator
                                 </SelectItem>
                              </SelectContent>
                           </Select>
                        </div>
                        <div>
                           <Label htmlFor="bulk-reason">Reason</Label>
                           <Input
                              id="bulk-reason"
                              placeholder="Reason for role change..."
                              value={bulkReason}
                              onChange={(e) => setBulkReason(e.target.value)}
                              className="mt-1"
                           />
                        </div>
                        <div className="flex items-end">
                           <Button
                              onClick={handleBulkRoleChange}
                              disabled={
                                 !bulkRole ||
                                 !bulkReason.trim() ||
                                 changeRoleMutation.isPending
                              }
                              className="w-full"
                           >
                              Update {selectedUsers.length} users
                           </Button>
                        </div>
                     </div>
                  </CardContent>
               </Card>
            )}

            {/* Users List */}
            <Card>
               <CardHeader>
                  <div className="flex items-center justify-between">
                     <CardTitle>Users ({filteredUsers.length})</CardTitle>
                     <div className="flex items-center gap-2">
                        <Checkbox
                           checked={
                              selectedUsers.length === filteredUsers.length &&
                              filteredUsers.length > 0
                           }
                           onCheckedChange={handleSelectAll}
                        />
                        <Label className="text-sm">Select All</Label>
                     </div>
                  </div>
               </CardHeader>
               <CardContent>
                  {isLoading ? (
                     <div className="space-y-4">
                        {Array.from({ length: 5 }).map((_, index) => (
                           <div
                              key={index}
                              className="flex items-center gap-4 p-4 border rounded-lg"
                           >
                              <Skeleton className="h-4 w-4" />
                              <Skeleton className="h-10 w-10 rounded-full" />
                              <div className="flex-1 space-y-2">
                                 <Skeleton className="h-4 w-48" />
                                 <Skeleton className="h-3 w-32" />
                              </div>
                              <Skeleton className="h-6 w-20" />
                              <Skeleton className="h-8 w-24" />
                           </div>
                        ))}
                     </div>
                  ) : error ? (
                     <div className="text-center py-8">
                        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                        <p className="text-red-600 mb-4">
                           Failed to load users
                        </p>
                        <Button onClick={() => refetch()} variant="outline">
                           Try Again
                        </Button>
                     </div>
                  ) : filteredUsers.length === 0 ? (
                     <div className="text-center py-8">
                        <User className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground">No users found</p>
                     </div>
                  ) : (
                     <div className="space-y-4">
                        {filteredUsers.map((user) => (
                           <div
                              key={user.id}
                              className="flex items-center gap-4 p-4 border rounded-lg hover:bg-muted/30 transition-colors"
                           >
                              <Checkbox
                                 checked={selectedUsers.includes(user.id)}
                                 onCheckedChange={(checked) =>
                                    handleSelectUser(
                                       user.id,
                                       checked as boolean
                                    )
                                 }
                              />
                              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                                 {user.role === "admin" ? (
                                    <Crown className="w-5 h-5 text-purple-600" />
                                 ) : (
                                    <User className="w-5 h-5 text-primary" />
                                 )}
                              </div>
                              <div className="flex-1">
                                 <div className="flex items-center gap-2 mb-1">
                                    <p className="font-medium">
                                       {user.firstName} {user.lastName}
                                    </p>
                                    {getRoleBadge(user.role)}
                                 </div>
                                 <p className="text-sm text-muted-foreground">
                                    {user.email}
                                 </p>
                                 <p className="text-xs text-muted-foreground">
                                    Account: {user.accountType} • Status:{" "}
                                    {user.verificationStatus}
                                 </p>
                              </div>
                              <div className="flex gap-2">
                                 <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-purple-600 border-purple-200 hover:bg-purple-50"
                                    onClick={() =>
                                       handleRoleChange(
                                          user.id,
                                          "admin",
                                          "Promoted to admin by admin"
                                       )
                                    }
                                    disabled={
                                       user.role === "admin" ||
                                       changeRoleMutation.isPending
                                    }
                                 >
                                    <Crown className="w-4 h-4 mr-1" />
                                    Make Admin
                                 </Button>
                                 <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-blue-600 border-blue-200 hover:bg-blue-50"
                                    onClick={() =>
                                       handleRoleChange(
                                          user.id,
                                          "user",
                                          "Demoted to user by admin"
                                       )
                                    }
                                    disabled={
                                       user.role === "user" ||
                                       changeRoleMutation.isPending
                                    }
                                 >
                                    <User className="w-4 h-4 mr-1" />
                                    Make User
                                 </Button>
                              </div>
                           </div>
                        ))}
                     </div>
                  )}
               </CardContent>
            </Card>
         </motion.div>
      </div>
   );
}
