import { Document, ObjectId } from "mongodb";

export interface UserMetrics extends Document {
  _id?: ObjectId;
  userId: ObjectId;
  monthlySpending: number;
  monthlyIncome: number;
  transactionCount: number;
  spendingChange: number; // Percentage change from last month
  incomeChange: number; // Percentage change from last month
  lastUpdated: Date;
  updatedBy?: ObjectId; // Admin who last updated these metrics
  createdAt: Date;
  updatedAt: Date;
}

export interface UserMetricsDetails {
  id: string;
  userId: string;
  monthlySpending: number;
  monthlyIncome: number;
  transactionCount: number;
  spendingChange: number;
  incomeChange: number;
  lastUpdated: Date;
  updatedBy?: string;
}

export interface CreateUserMetricsData {
  userId: string;
  monthlySpending: number;
  monthlyIncome: number;
  transactionCount: number;
  spendingChange?: number;
  incomeChange?: number;
  updatedBy?: string;
}

export interface UpdateUserMetricsData {
  monthlySpending?: number;
  monthlyIncome?: number;
  transactionCount?: number;
  spendingChange?: number;
  incomeChange?: number;
  updatedBy?: string;
}
